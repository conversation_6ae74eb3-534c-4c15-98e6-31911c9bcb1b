﻿using Fattail.CreativeManagement.API.Domain.CreativeFields;
using Fattail.CreativeManagement.API.Domain.CreativeFiles;
using Fattail.CreativeManagement.API.Domain.Creatives.Fields;
using Fattail.CreativeManagement.API.Domain.Creatives.Fields.Validators;
using Fattail.CreativeManagement.API.Domain.Creatives.Fields.Validators.RequiredValidators;
using FluentAssertions;
using NUnit.Framework;
using static FluentAssertions.FluentActions;

namespace Fattail.CreativeManagement.API.Tests.Domain.Creatives.Fields.Validators.RequiredValidators;

[TestFixture]
public class RequiredRuleValidatorsFactoryTests
{
    private CreativeFieldIdentifier _creativeFieldIdentifier;

    [SetUp]
    public void SetUp ()
    {
        var creativeFieldId = new CreativeFieldId(123456);
        CreativeFieldType creativeFieldType = CreativeFieldType.MultiFileUpload;
        _creativeFieldIdentifier = CreativeFieldIdentifier.Create(creativeFieldId, creativeFieldType);
    }

    [Test]
    public void String_required_rule_validator_can_be_created ()
    {
        IRuleValidator<string> result = RequiredRuleValidatorsFactory.GetValidator<string>(_creativeFieldIdentifier);

        result.Should().NotBeNull().And.BeOfType<StringRequiredRuleValidator>();
    }

    [Test]
    public void Creative_files_required_rule_validator_can_be_created ()
    {
        IRuleValidator<IReadOnlyList<CreativeFile?>> result =
            RequiredRuleValidatorsFactory.GetValidator<IReadOnlyList<CreativeFile?>>(_creativeFieldIdentifier);

        result.Should().NotBeNull().And.BeOfType<CollectionsRequiredRuleValidator<IReadOnlyList<CreativeFile?>, CreativeFile?>>();
    }

    [Test]
    public void Multi_select_option_required_rule_validator_can_be_created ()
    {
        IRuleValidator<IEnumerable<long>> result =
            RequiredRuleValidatorsFactory.GetValidator<IEnumerable<long>>(_creativeFieldIdentifier);

        result.Should().NotBeNull().And.BeOfType<CollectionsRequiredRuleValidator<IEnumerable<long>, long>>();
    }

    [Test]
    public void Long_required_rule_validator_can_be_created ()
    {
        IRuleValidator<long?> result =
            RequiredRuleValidatorsFactory.GetValidator<long?>(_creativeFieldIdentifier);

        result.Should().NotBeNull().And.BeOfType<LongRequiredRuleValidator>();
    }

    [Test]
    public void Required_rule_validator_can_not_be_created ()
    {
        Invoking(() =>
                RequiredRuleValidatorsFactory.GetValidator<IEnumerable<string>>(_creativeFieldIdentifier))
            .Should().ThrowExactly<NotImplementedException>();
    }
}