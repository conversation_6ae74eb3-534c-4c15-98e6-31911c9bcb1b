﻿using Fattail.CreativeManagement.API.Domain.Common;
using Fattail.CreativeManagement.API.Domain.CreativeFields;
using Fattail.CreativeManagement.API.Domain.Creatives.Errors;
using Fattail.CreativeManagement.API.Domain.Creatives.Fields.Sanitizers.StrategyProvider;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates.Fields;
using FluentResults;

namespace Fattail.CreativeManagement.API.Domain.Creatives.Fields;

public abstract class CreativeFieldValue : ValueObject
{
    protected CreativeFieldValue (CreativeFieldIdentifier creativeFieldIdentifier)
    {
        CreativeFieldIdentifier = creativeFieldIdentifier;
    }

    public CreativeFieldIdentifier CreativeFieldIdentifier { get; }

    public abstract Task<Result<CreativeFieldValue>> GenerateNewValue (object? value, IEnumerable<ValidationRule> validationRules,
        ISanitizerStrategyProvider? sanitizerStrategyProvider = null);

    protected override IEnumerable<object?> GetEqualityComponents ()
    {
        yield return CreativeFieldIdentifier;
    }

    public static async Task<Result<CreativeFieldValue>> Create (CreativeFieldIdentifier creativeFieldIdentifier, object? value,
        IEnumerable<ValidationRule> validationRules, ISanitizerStrategyProvider? sanitizerStrategyProvider = null)
    {
        return creativeFieldIdentifier.Type
            switch
        {
            CreativeFieldType.MultiFileUpload => (await MultiFileUploadFieldValue.Create(sanitizerStrategyProvider, creativeFieldIdentifier, value, validationRules)).ToResult<CreativeFieldValue>(creativeFieldValue => creativeFieldValue),
            CreativeFieldType.FileUpload => (await FileUploadFieldValue.Create(sanitizerStrategyProvider, creativeFieldIdentifier, value, validationRules)).ToResult<CreativeFieldValue>(creativeFieldValue => creativeFieldValue),
            CreativeFieldType.SingleLineText => (await SingleLineTextFieldValue.Create(creativeFieldIdentifier, value, validationRules)).ToResult<CreativeFieldValue>(creativeFieldValue => creativeFieldValue),
            CreativeFieldType.MultiSelectOption => (await MultiSelectOptionFieldValue.Create(creativeFieldIdentifier, value, validationRules)).ToResult<CreativeFieldValue>(creativeFieldValue => creativeFieldValue),
            CreativeFieldType.SingleSelectOption => (await SingleSelectOptionFieldValue.Create(creativeFieldIdentifier, value, validationRules)).ToResult<CreativeFieldValue>(creativeFieldValue => creativeFieldValue),
            CreativeFieldType.MultiLineText => (await MultiLineTextFieldValue.Create(creativeFieldIdentifier, value, validationRules)).ToResult<CreativeFieldValue>(creativeFieldValue => creativeFieldValue),
            CreativeFieldType.SectionDivider => Result.Fail(new CreativeFieldValueNotAdmittedError(creativeFieldIdentifier.Type, nameof(CreativeFieldValue))),
            _ => throw new NotImplementedException()
        };
    }

    public static async Task<Result<CreativeFieldValue>> CreateWithoutValue (CreativeFieldIdentifier creativeFieldIdentifier, IEnumerable<ValidationRule> validationRules)
    {
        return await Create(creativeFieldIdentifier, null, validationRules);
    }
}

public abstract class CreativeFieldValue<TValue> : CreativeFieldValue
{
    protected CreativeFieldValue (CreativeFieldIdentifier creativeFieldIdentifier, TValue? value) : base(creativeFieldIdentifier)
    {
        Value = value;
    }

    public TValue? Value { get; set; }

    protected override IEnumerable<object?> GetEqualityComponents ()
    {
        yield return base.GetEqualityComponents();
        yield return Value;
    }
}
