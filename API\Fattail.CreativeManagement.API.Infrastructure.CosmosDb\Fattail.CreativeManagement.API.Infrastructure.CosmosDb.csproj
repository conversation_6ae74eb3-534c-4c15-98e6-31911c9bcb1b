﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <AzureCosmosDisableNewtonsoftJsonCheck>true</AzureCosmosDisableNewtonsoftJsonCheck>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="AutoMapper" Version="12.0.1" />
    <PackageReference Include="AutoMapper.Extensions.Microsoft.DependencyInjection" Version="12.0.1" />
    <PackageReference Include="JsonSubTypes" Version="2.0.1" />
    <PackageReference Include="Microsoft.Azure.Cosmos" Version="3.52.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="9.0.6" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="9.0.6" />
    <PackageReference Include="Microsoft.Extensions.Options.ConfigurationExtensions" Version="9.0.6" />
    <PackageReference Include="Polly" Version="8.5.2" />
    <PackageReference Include="SkiaSharp" Version="3.116.1" />
    <PackageReference Include="System.Drawing.Common" Version="9.0.0" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Fattail.CreativeManagement.API.Application\Fattail.CreativeManagement.API.Application.csproj" />
    <ProjectReference Include="..\Fattail.CreativeManagement.API.Domain\Fattail.CreativeManagement.API.Domain.csproj" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Serializers\" />
  </ItemGroup>
</Project>