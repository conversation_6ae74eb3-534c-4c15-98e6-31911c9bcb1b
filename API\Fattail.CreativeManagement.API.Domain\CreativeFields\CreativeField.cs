﻿using Fattail.CreativeManagement.API.Domain.Common;
using Fattail.CreativeManagement.API.Domain.Common.Errors;
using Fattail.CreativeManagement.API.Domain.CreativeFields.Errors;
using FluentResults;

namespace Fattail.CreativeManagement.API.Domain.CreativeFields;

public class CreativeField : Entity<CreativeFieldId>
{
    protected CreativeField (CreativeFieldId id, string name, CreativeFieldType type, bool predefined, string? omsExternalIdentifier) : base(id)
    {
        Name = name;
        Type = type;
        Predefined = predefined;
        OmsExternalIdentifier = omsExternalIdentifier;
    }

    public string Name { get; }

    public CreativeFieldType Type { get; }
    
    public bool Predefined { get; }
    public string? OmsExternalIdentifier { get; }


    public static Result<CreativeField> Create (CreativeFieldId id, string? name, CreativeFieldType type, CreativeFieldUniqueNameRequirement creativeFieldUniqueNameRequirement, bool predefined, string? omsExternalIdentifier)
    {
        Result result = CanCreateCreativeField(name, type, creativeFieldUniqueNameRequirement);

        if (result.IsFailed)
        {
            return result;
        }

        var creativeField = new CreativeField(id, name!, type, predefined, omsExternalIdentifier);

        return creativeField;
    }

    protected static Result CanCreateCreativeField(string? name, CreativeFieldType type, CreativeFieldUniqueNameRequirement creativeFieldUniqueNameRequirement)
    {
        if (string.IsNullOrWhiteSpace(name))
        {
            return Result.Fail(new RequiredValueMissingError("creative field name", nameof(CreativeField)));
        }
        
        if (!creativeFieldUniqueNameRequirement.IsSatisfied)
        {
            return Result.Fail(new CreativeFieldNameInUseError(name, type.ToString(), nameof(CreativeField)));
        }

        return Result.Ok();
    }
}