﻿using AutoMapper;
using Fattail.CreativeManagement.API.Application.CreativeTemplates;
using Fattail.CreativeManagement.API.Application.CreativeTemplates.CreativeTemplateAdd;
using Fattail.CreativeManagement.API.Domain.CreativeFields;
using Fattail.CreativeManagement.API.Infrastructure.QueryServices.CreativeFields;
using Fattail.CreativeManagement.API.Infrastructure.QueryServices.CreativeFields.GetById;
using MediatR;
using System.Collections.ObjectModel;

namespace Fattail.CreativeManagement.API.Azure.Endpoints.CreativeTemplates.Common;

public class CreativeTemplateAddCustomMapper : ITypeConverter<CreativeTemplateAddRequest, CreativeTemplateAddCommand>
{
    private IMediator _mediator;

    private readonly List<string> _defaultExtensions = new()
    {
        ".png",
        ".svg",
        ".jpeg",
        ".gif",
        ".pdf",
        ".mp4",
        ".xlsx",
        ".xls",
        ".doc",
        ".psd",
        ".txt",
        ".zip",
        ".jpg",
        ".docx",
        ".ppt",
        ".pptx"
    };

    private readonly List<CreativeFieldType> _uploadFileCreativeFieldTypes = new()
    {
        CreativeFieldType.FileUpload, CreativeFieldType.MultiFileUpload
    };

    public CreativeTemplateAddCustomMapper (IMediator mediator)
    {
        _mediator = mediator;
    }

    public CreativeTemplateAddCommand Convert (CreativeTemplateAddRequest source,
        CreativeTemplateAddCommand destination,
        ResolutionContext context)
    {
        HashSet<long> creativeFieldIds = source.CreativeFieldIds == null ? new HashSet<long>() : source.CreativeFieldIds;

        int fieldOrderCounter = 0;
        ReadOnlyCollection<CreativeTemplateCreativeFieldDto> creativeFields = creativeFieldIds.Select(x =>
            new CreativeTemplateCreativeFieldDto(x, fieldOrderCounter++,
                GetValidationsRulesForUploadCreativeFieldType(x).Result, null, new Dictionary<string, object?>())).ToList().AsReadOnly();
        
        return new CreativeTemplateAddCommand(source.Name, creativeFields);

    }

    private async Task<bool> IsUploadCreativeFieldType (long creativeFieldId)
    {
        CreativeFieldQueryResult? creativeField =
            await _mediator.Send( new CreativeFieldGetByIdQuery(creativeFieldId.ToString()));
        return creativeField != null && _uploadFileCreativeFieldTypes.Contains(creativeField.Type);
    }

    private async Task<List<ValidationRuleDto>> GetValidationsRulesForUploadCreativeFieldType (long creativeFieldId)
    {
        if (await IsUploadCreativeFieldType(creativeFieldId))
        {
            return  new List<ValidationRuleDto>
            {
                new(Domain.CreativeTemplates.CreativeFieldValidationRuleType.FileUploadExtensions, _defaultExtensions)
            };
        }

        return new List<ValidationRuleDto>();
    }
}