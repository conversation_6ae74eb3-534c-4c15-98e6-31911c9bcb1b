using Fattail.CreativeManagement.API.Domain.CreativeFields;

namespace Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Entities;

public class CreativeField : Entity
{
    public string Name { get; set; }
    
    public CreativeFieldType Type { get; set; }

    public bool Predefined { get; set; } = false;

    public string? OmsExternalIdentifier { get; set; }
}

public class SelectOption
{
    public string Id { get; set; } = null!;
    public string Description { get; set; }
};

public class MultiSelectCreativeField : CreativeField
{
    public SelectOption[] Options { get; set; }
}

public class SingleSelectCreativeField : CreativeField
{
    public SelectOption[] Options { get; set; }
}