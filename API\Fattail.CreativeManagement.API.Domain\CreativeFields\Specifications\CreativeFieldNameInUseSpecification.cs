﻿using Fattail.CreativeManagement.API.Domain.Repositories;
using System.Linq.Expressions;

namespace Fattail.CreativeManagement.API.Domain.CreativeFields.Specifications;
public class CreativeFieldNameInUseSpecification (string? name, CreativeFieldType? type) : Specification<CreativeField>
{
    public override Expression<Func<CreativeField, bool>> ToExpression ()
    {
        string? stringType = type == null ? null : type.ToString();
        return creativeField => creativeField.Name == name && creativeField.Type.ToString() == stringType;
    }
}
