﻿using Fattail.CreativeManagement.API.Domain.Common.Errors;
using Fattail.CreativeManagement.API.Domain.CreativeFields;
using Fattail.CreativeManagement.API.Domain.Creatives;
using Fattail.CreativeManagement.API.Domain.Creatives.Factory;
using Fattail.CreativeManagement.API.Domain.Creatives.Fields;
using Fattail.CreativeManagement.API.Domain.Creatives.Statuses;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates.Factory;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates.Fields;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates.Requirements;
using FluentAssertions;
using FluentResults;
using FluentResults.Extensions.FluentAssertions;
using Moq;
using NUnit.Framework;

namespace Fattail.CreativeManagement.API.Tests.Domain.Creatives;

[TestFixture]
public class CreateCreativeTests : CreativeTestsBase
{
    [TestCase("")]
    [TestCase(" ")]
    [TestCase(null)]
    public async Task Creative_cant_be_created_without_name (string? name)
    {
        CreativeTemplate creativeTemplate = CreateCreativeTemplate();

        Result<Creative> creativeResult = await _creativeFactory.Create(new CreateCreativeRequest(
            12345,
            name!,
            creativeTemplate,
            6789,
            null,
            12345,
            new HashSet<long>(),
            "updater",
            new Dictionary<CreativeFieldId, object?>(),
            DateTime.Now));

        creativeResult.Should().BeFailure()
            .And.HaveReason(new RequiredValueMissingError("Name", nameof(Creative)));
    }

    [Test]
    public async Task Creative_cant_be_created_without_template ()
    {
        Result<Creative> creativeResult =
            await _creativeFactory.Create(new CreateCreativeRequest(12345, "creative name", null,
                6789, null, 12345, new HashSet<long>(),
                "Updater", new Dictionary<CreativeFieldId, object?>(), DateTime.Now));

        creativeResult.Should().BeFailure()
            .And.HaveReason(new InvalidValueError("creative template", nameof(Creative)));
    }

    [TestCase(0)]
    public async Task Creative_cant_be_created_without_campaign (long campaignId)
    {
        CreativeTemplate creativeTemplate = CreateCreativeTemplate();

        Result<Creative> creativeResult = await _creativeFactory.Create(new CreateCreativeRequest(
            12345,
            "creative name",
            creativeTemplate,
            6789,
            null,
            campaignId,
            new HashSet<long>(),
            "updater",
            new Dictionary<CreativeFieldId, object?>(),
            DateTime.Now));

        creativeResult.Should().BeFailure()
            .And.HaveReason(new RequiredValueMissingError("campaign", nameof(Creative)));
    }

    [TestCase("")]
    [TestCase(" ")]
    [TestCase(null)]
    public async Task Creative_cant_be_created_without_updater (string? updater)
    {
        CreativeTemplate creativeTemplate = CreateCreativeTemplate();

        Result<Creative> creativeResult = await _creativeFactory.Create(new CreateCreativeRequest(
            12345,
            "creative name",
            creativeTemplate,
            6789,
            null,
            12345,
            new HashSet<long>(),
            updater!,
            new Dictionary<CreativeFieldId, object?>(),
            DateTime.Now));

        creativeResult.Should().BeFailure()
            .And.HaveReason(new RequiredValueMissingError("UpdatedBy", nameof(Creative)));
    }

    [TestCase(0)]
    public async Task Creative_can_not_be_created_with_an_invalid_ad_book_client (long adBookClientId)
    {
        CreativeTemplate creativeTemplate = CreateCreativeTemplate();

        Result<Creative> creativeResult = await _creativeFactory.Create(new CreateCreativeRequest(
            12345,
            "creative name",
            creativeTemplate,
            adBookClientId,
            null,
            12345,
            new HashSet<long>(),
            "updater",
            new Dictionary<CreativeFieldId, object?>(),
            DateTime.Now));

        creativeResult.Should().BeFailure()
            .And.HaveReason(new RequiredValueMissingError("AdBook client", nameof(Creative)));
    }

    [Test]
    public async Task Creative_can_be_created_without_an_ad_book_client ()
    {
        CreativeTemplate creativeTemplate = CreateCreativeTemplate();

        long? adBookClientId = null;

        Result<Creative> creativeResult = await _creativeFactory.Create(new CreateCreativeRequest(
            12345,
            "creative name",
            creativeTemplate,
            adBookClientId,
            null,
            12345,
            new HashSet<long>(),
            "updater",
            new Dictionary<CreativeFieldId, object?>(),
            DateTime.Now));

        creativeResult.Should().BeSuccess();
        creativeResult.Value.AdBookClientId.Should().BeNull();
    }

    [Test]
    public async Task Creative_can_be_created ()
    {
        (DateTime lastUpdatedOn, CreativeTemplate creativeTemplate, string creativeName, long adBookClientId, long campaignId,
            string updater, Result<Creative> creativeResult) = await CreateCreative();

        creativeResult.Should().BeSuccess();

        Creative creative = creativeResult.Value;

        creative.Name.Should().Be(creativeName);
        creative.CreativeTemplateId.Should().Be(creativeTemplate.Id);
        creative.AdBookClientId.Should().Be(adBookClientId);
        creative.CampaignId.Should().Be(campaignId);
        creative.LastUpdatedBy.Should().Be(updater);
        creative.LastUpdatedOn.Should().Be(lastUpdatedOn);
        creative.Fields.Should().BeEquivalentTo(creativeTemplate.CreativeFields.Where(ctf => ctf.Type != CreativeFieldType.SectionDivider),
                options => options.ExcludingMissingMembers()
                    .ComparingByMembers<CreativeTemplateCreativeField>())
            .And.AllSatisfy(creativeFieldValue => creativeFieldValue.Should()
                .BeAssignableTo<MultiFileUploadFieldValue>().Which.Value.Should().BeNullOrEmpty());
    }

    [Test]
    public async Task Creative_can_be_created_with_initial_field_values ()
    {
        var initialFieldValues = new Dictionary<CreativeFieldId, object?>
        {
            { new CreativeFieldId(11), "Test value" }, { new CreativeFieldId(12), "Test value2" }
        };
        (DateTime lastUpdatedOn, CreativeTemplate creativeTemplate, string creativeName, long adBookClientId, long campaignId,
                string updater, Result<Creative> creativeResult) =
            await CreateCreativeWithInitialFieldValues(initialFieldValues);

        creativeResult.Should().BeSuccess();

        Creative creative = creativeResult.Value;

        creative.Name.Should().Be(creativeName);
        creative.CreativeTemplateId.Should().Be(creativeTemplate.Id);
        creative.AdBookClientId.Should().Be(adBookClientId);
        creative.CampaignId.Should().Be(campaignId);
        creative.LastUpdatedBy.Should().Be(updater);
        creative.LastUpdatedOn.Should().Be(lastUpdatedOn);
        creative.Fields.Should().BeEquivalentTo(creativeTemplate.CreativeFields,
                options => options.ExcludingMissingMembers()
                    .ComparingByMembers<CreativeTemplateCreativeField>()
                    .Excluding(creativeTemplateCreativeField => creativeTemplateCreativeField.DisplayOrder))
            .And.AllSatisfy(creativeFieldValue => creativeFieldValue.Should()
                .BeAssignableTo<SingleLineTextFieldValue>().Which.Value.Should().NotBeNullOrEmpty());
    }

    [Test]
    public async Task Creative_is_created_without_section_divider_creative_fields ()
    {
        (DateTime lastUpdatedOn, CreativeTemplate creativeTemplate, string creativeName, long adBookClientId, long campaignId,
            string updater, Result<Creative> creativeResult) = await CreateCreative();

        creativeResult.Should().BeSuccess();

        Creative creative = creativeResult.Value;

        creative.Fields.Should().NotContain(f => f.CreativeFieldIdentifier.Type == CreativeFieldType.SectionDivider);
    }

    [Test]
    [Category("Creative status")]
    public async Task Creative_is_created_with_pending_approval_status ()
    {
        (DateTime _, CreativeTemplate _, string _, long _, long _,
            string _, Result<Creative> creativeResult) = await CreateCreative();

        Creative creative = creativeResult.Value;

        creative.Status.Should().Be(CreativeStatus.PendingApproval);
        creative.LastApproval.Should().BeNull();
    }

    private CreativeTemplate CreateCreativeTemplate ()
    {
        var createCreativeTemplateRequest = new CreativeTemplateCreateRequest(
            new CreativeTemplateId(1234),
            CreativeTemplateName.Create("creative template name", new CreativeTemplateUniqueNameRequirement(true)).Value,
            CreativeType.Undefined,
            [new(new(1), 1)],
            new HashSet<CreativeField>([ CreativeField.Create(new(1), "name", CreativeFieldType.SingleLineText, _creativeFieldUniqueNameRequirement, false, null).Value ]),
            false
        );

        return CreativeTemplateFactory.Create(createCreativeTemplateRequest).Value;
    }
}