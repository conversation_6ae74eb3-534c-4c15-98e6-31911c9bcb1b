﻿using Fattail.CreativeManagement.API.Domain.CreativeFields;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates.Factory;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates.Requirements;
using FluentAssertions;
using Moq;
using NUnit.Framework;

namespace Fattail.CreativeManagement.API.Tests.Domain.CreativeTemplates;

[TestFixture]
public class EditCreativeTemplateTests
{
    private readonly string _fieldName = "fieldName";

    [Test]
    public async Task Creative_template_can_be_updated ()
    {
        CreativeTemplateName creativeTemplateName = CreativeTemplateName.Create("New creative template", new CreativeTemplateUniqueNameRequirement(true)).Value;

        var creativeFieldUniqueNameRequirement = new CreativeFieldUniqueNameRequirement(true);

        var createCreativeTemplateRequest = new CreativeTemplateCreateRequest(
            new CreativeTemplateId(1234),
            CreativeTemplateName.Create("Test creative template", new CreativeTemplateUniqueNameRequirement(true)).Value,
            CreativeType.Undefined,
            [new(new(123), 1), new(new(456), 2)],
            new HashSet<CreativeField>(
            [
                CreativeField.Create(new CreativeFieldId(123), _fieldName, It.IsAny<CreativeFieldType>(), creativeFieldUniqueNameRequirement, false, null).Value,
                CreativeField.Create(new CreativeFieldId(456), _fieldName, It.IsAny<CreativeFieldType>(), creativeFieldUniqueNameRequirement, false, null).Value
            ]),
            false
        );

        CreativeTemplate creativeTemplate = CreativeTemplateFactory.Create(createCreativeTemplateRequest).Value;

        creativeTemplate.EditName(creativeTemplateName);

        creativeTemplate.Name.Should().Be(creativeTemplateName);
    }
}