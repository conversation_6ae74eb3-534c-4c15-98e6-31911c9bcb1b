﻿using Fattail.CreativeManagement.API.Domain.CreativeFields;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates;

namespace Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Entities;

public sealed class CreativeTemplate : Entity
{
    public string Name { get; set; } = null!;

    public CreativeType CreativeType { get; set; } = CreativeType.Undefined;

    public CreativeTemplateCreativeField[] CreativeFields { get; set; } = [];

    public bool Archive { get; set; } = false;

    public bool Predefined { get; set; } = false;

    public long? ClonedFrom { get; set; }
}

public sealed class ValidationRule
{
    public CreativeFieldValidationRuleType Type { get; set; }

    public string[] Options { get; set; } = [];
};

public class CreativeTemplateCreativeField
{
    public string Id { get; set; } = null!;
    public string Name { get; set; } = null!;
    public int DisplayOrder { get; set; }
    public CreativeFieldType Type { get; set; }
    public ValidationRule[] ValidationRules { get; set; } = [];
    public string? Tooltip { get; set; }
}

public record CreativeTemplateSelectOption
{
    public string Id { get; set; } = null!;
    public string Description { get; set; }
};

public sealed class CreativeTemplateMultiSelectCreativeField : CreativeTemplateCreativeField
{
    public CreativeTemplateSelectOption[] Options { get; set; } = [];
}

public sealed class CreativeTemplateSingleSelectCreativeField : CreativeTemplateCreativeField
{
    public CreativeTemplateSelectOption[] Options { get; set; } = [];
}

public sealed class CreativeTemplateSectionDividerCreativeField : CreativeTemplateCreativeField
{
    public string? Content { get; set; }
}