﻿using AutoBogus;
using Bogus;
using Bogus.DataSets;
using Fattail.CreativeManagement.API.Application.Creatives;
using Fattail.CreativeManagement.API.Application.Creatives.CreativeMigrate;
using Fattail.CreativeManagement.API.Domain.Common;
using Fattail.CreativeManagement.API.Domain.Common.Errors;
using Fattail.CreativeManagement.API.Domain.CreativeFields;
using Fattail.CreativeManagement.API.Domain.CreativeFiles;
using Fattail.CreativeManagement.API.Domain.CreativeFiles.Errors;
using Fattail.CreativeManagement.API.Domain.CreativeFiles.Types;
using Fattail.CreativeManagement.API.Domain.Creatives;
using Fattail.CreativeManagement.API.Domain.Creatives.Factory;
using Fattail.CreativeManagement.API.Domain.Creatives.Fields.Sanitizers;
using Fattail.CreativeManagement.API.Domain.Creatives.Fields.Sanitizers.StrategyProvider;
using Fattail.CreativeManagement.API.Domain.Creatives.Fields.Validators;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates.Factory;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates.Fields;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates.Requirements;
using Fattail.CreativeManagement.API.Domain.Repositories;
using FluentResults;
using FluentResults.Extensions.FluentAssertions;
using Moq;
using NUnit.Framework;

namespace Fattail.CreativeManagement.API.Tests.Application.CreativeMigrate;

[TestFixture]
public class CreativeFilesMigrateRollbackHandlerTests
{
    private const long CreativeTemplateId = 574650397348900;
    private const long CreativeFieldFileId = 574650397348901;
    private const long CreativeFieldDestinationUrlId = 574650397348902;
    private const long CreativeFieldApplyCreativeToFuturePlacementId = 574650397348903;
    private const long CreativeFieldCommentsId = 574650397348904;

    [SetUp]
    public void SetUp ()
    {
        _idManagerMock = new Mock<IIdManager>();
        _idManagerMock.Setup(idManager => idManager.GetId()).Returns(new Faker().Random.Long(1));

        _dateTimeProviderMock = new Mock<IDateTimeProvider>();
        _creativeTemplateRepositoryMock = new Mock<ICreativeTemplateRepository>();
        _creativeRepositoryMock = new Mock<ICreativeRepository>();
        _creativeFileRepositoryMock = new Mock<ICreativeFileRepository>();
        _sanitizerStrategyProviderMock = new Mock<ISanitizerStrategyProvider>();
        _creativeFilesManager = new Mock<ICreativeFilesManager>();

        _sanitizerStrategyProviderMock = new Mock<ISanitizerStrategyProvider>();
        _sanitizerMock = new Mock<ISanitizer>();

        _sanitizerMock.Setup(sanitizer => sanitizer.Sanitize<IReadOnlyList<CreativeFile>>(It.IsAny<object>()))
            .ReturnsAsync(new List<CreativeFile>() { });

        _creativeFactory = new CreativeFactory(_sanitizerStrategyProviderMock.Object);

        _sanitizerStrategyProviderMock
            .Setup(sanitizerStrategyProvider =>
                sanitizerStrategyProvider.GetFrom(CreativeFieldType.MultiFileUpload))
            .Returns(_sanitizerMock.Object);

        _creativeMigrateHandler = new CreativeMigrateHandler(
            _creativeTemplateRepositoryMock.Object,
            _creativeRepositoryMock.Object,
            _creativeFileRepositoryMock.Object,
            _creativeFactory,
            _dateTimeProviderMock.Object,
            _sanitizerStrategyProviderMock.Object,
            _idManagerMock.Object);
    }

    private CreativeMigrateHandler _creativeMigrateHandler = null!;
    private Mock<IIdManager> _idManagerMock = null!;
    private Mock<IDateTimeProvider> _dateTimeProviderMock = null!;
    private Mock<ICreativeTemplateRepository> _creativeTemplateRepositoryMock = null!;
    private Mock<ICreativeRepository> _creativeRepositoryMock = null!;
    private Mock<ICreativeFileRepository> _creativeFileRepositoryMock = null!;
    private Mock<ISanitizerStrategyProvider> _sanitizerStrategyProviderMock = null!;
    private Mock<ISanitizer> _sanitizerMock = null!;
    private Mock<ICreativeFilesManager> _creativeFilesManager = null!;
    private ICreativeFactory _creativeFactory = null!;

    [Test]
    public async Task Creative_template_not_found ()
    {
        CreativeMigrateCommand migrateCommand = new AutoFaker<CreativeMigrateCommand>()
            .RuleFor(addCommand => addCommand.CreativeTemplateId, 0)
            .Generate();

        Result<CreativeResult> result = await _creativeMigrateHandler.Handle(migrateCommand, CancellationToken.None);

        result.Should().BeFailure().And.HaveReason(new EntityNotFoundError(migrateCommand.CreativeTemplateId.ToString(), nameof(CreativeTemplate)));
        _creativeTemplateRepositoryMock.Verify(
            creativeTemplateRepository => creativeTemplateRepository.FindByIdAsync(It.IsAny<CreativeTemplateId>()),
            Times.Never);
    }

    [Test]
    public async Task? Migrated_creative_was_updated_by_cms_user ()
    {
        var uniqueNameRequirement = new CreativeFieldUniqueNameRequirement(true);

        var createCreativeTemplateRequest = new CreativeTemplateCreateRequest(
            new CreativeTemplateId(123),
            CreativeTemplateName.Create("creative template 1", new CreativeTemplateUniqueNameRequirement(true)).Value,
            CreativeType.Undefined,
            [
                new(new(CreativeFieldFileId), 1),
                new(new(CreativeFieldDestinationUrlId), 2),
                new(new(CreativeFieldApplyCreativeToFuturePlacementId), 3),
                new(new(CreativeFieldCommentsId), 4)
            ],
            new HashSet<CreativeField>([
                CreativeField.Create(new(CreativeFieldFileId), "File", CreativeFieldType.MultiFileUpload, uniqueNameRequirement, false, null).Value,
                CreativeField.Create(new(CreativeFieldDestinationUrlId), "Destination Url", CreativeFieldType.SingleLineText, uniqueNameRequirement, false, null).Value,
                CreativeField.Create(new(CreativeFieldApplyCreativeToFuturePlacementId), "Apply Creative To Future Placement", CreativeFieldType.SingleLineText, uniqueNameRequirement, false, null).Value,
                CreativeField.Create(new(CreativeFieldCommentsId), "Comments", CreativeFieldType.MultiLineText, uniqueNameRequirement, false, null).Value
            ]),
            false
        );

        CreativeTemplate creativeTemplate = CreativeTemplateFactory.Create(createCreativeTemplateRequest).Value;

        CreativeMigrateCommand migrateCommand = new AutoFaker<CreativeMigrateCommand>()
            .RuleFor(migrateCommand => migrateCommand.CreativeTemplateId, CreativeTemplateId)
            .RuleFor(migrateCommand => migrateCommand.CreativeFieldFileId, CreativeFieldFileId)
            .RuleFor(migrateCommand => migrateCommand.AdBookClientId, 0)
            .RuleFor(migrateCommand => migrateCommand.CampaignId, 0)
            .RuleFor(migrateCommand => migrateCommand.AdBookAdId, 0)
            .RuleFor(migrateCommand => migrateCommand.Fields, faker => creativeTemplate.CreativeFields
                .Select<CreativeTemplateCreativeField, CreativeFieldDto>(creativeField =>
                    creativeField.Type switch
                    {
                        CreativeFieldType.MultiFileUpload => new MultiFileUploadFieldValue(
                            CreativeFieldFileId, faker.Make(3, () => faker.Random.Long(1)).AsReadOnly()),
                        CreativeFieldType.SingleLineText => new SingleLineTextFieldValue(
                            creativeField.Id, faker.Random.String()),
                        CreativeFieldType.MultiLineText => new MultiLineTextFieldValue(
                            creativeField.Id, faker.Random.String()),
                        CreativeFieldType.MultiSelectOption => new MultiSelectOptionFieldValue(
                            creativeField.Id, faker.Make(3, () => faker.Random.Long(1)).AsReadOnly()),
                        _ => throw new ArgumentOutOfRangeException()
                    }).ToList())
            .Generate();

        _creativeTemplateRepositoryMock
            .Setup(creativeTemplateRepository =>
                creativeTemplateRepository.FindByIdAsync(It.IsAny<CreativeTemplateId>()))
            .ReturnsAsync(creativeTemplate);

        _creativeRepositoryMock.Setup(creativeRepository => creativeRepository.FindMigratedAdBookCreativeAsync(It.IsAny<long>(), It.IsAny<long>(), It.IsAny<long>()))
            .ReturnsAsync(Result.Fail(new MigratedCreativeFileIsUpdatedError(migrateCommand.AdBookAdId, migrateCommand.AdBookClientId, migrateCommand.CampaignId)));

        CreativeFile fakeCreativeFile = new AutoFaker<CreativeFile>()
            .RuleFor(c => c.Name, faker => CreativeFileName.From(faker.System.FileName()))
            .RuleFor(c => c.Size, faker => faker.Random.Long(1, 1000))
            .RuleFor(c => c.Type, faker => CreativeFileType.Image)
            .RuleFor(c => c.Metadata, new CreativeFileMetadata(new Dictionary<string, string>()))
            .Generate();

        _creativeFileRepositoryMock.Setup(creativeFileRepository =>
                creativeFileRepository.FindByIdAsync(It.IsAny<CreativeFileId>()))
            .ReturnsAsync(fakeCreativeFile);

        Result<CreativeResult> result = await _creativeMigrateHandler.Handle(migrateCommand, CancellationToken.None);

        result.Should().BeFailure().And.HaveReason(new MigratedCreativeFileIsUpdatedError(migrateCommand.AdBookAdId, migrateCommand.AdBookClientId, migrateCommand.CampaignId));
        _creativeRepositoryMock.Verify(
            creativeRepository => creativeRepository.FindMigratedAdBookCreativeAsync(It.IsAny<long>(), It.IsAny<long>(), It.IsAny<long>()),
            Times.Once);
    }

    [Test]
    public async Task Creative_migrated_successfully ()
    {
        var uniqueNameRequirement = new CreativeFieldUniqueNameRequirement(true);

        var createCreativeTemplateRequest = new CreativeTemplateCreateRequest(
            new CreativeTemplateId(123),
            CreativeTemplateName.Create("creative template 1", new CreativeTemplateUniqueNameRequirement(true)).Value,
            CreativeType.Undefined,
            [
                new(new(CreativeFieldFileId), 1),
                new(new(CreativeFieldDestinationUrlId), 2),
                new(new(CreativeFieldApplyCreativeToFuturePlacementId), 3),
                new(new(CreativeFieldCommentsId), 4)],
            new HashSet<CreativeField>([
                CreativeField.Create(new(CreativeFieldFileId), "File", CreativeFieldType.MultiFileUpload, uniqueNameRequirement, false, null).Value,
                CreativeField.Create(new(CreativeFieldDestinationUrlId), "Destination Url", CreativeFieldType.SingleLineText, uniqueNameRequirement, false, null).Value,
                CreativeField.Create(new(CreativeFieldApplyCreativeToFuturePlacementId), "Apply Creative To Future Placement", CreativeFieldType.SingleLineText, uniqueNameRequirement, false, null).Value,
                CreativeField.Create(new(CreativeFieldCommentsId), "Comments", CreativeFieldType.MultiLineText, uniqueNameRequirement, false, null).Value
            ]),
            false
        );

        CreativeTemplate creativeTemplate = CreativeTemplateFactory.Create(createCreativeTemplateRequest).Value;

        CreativeMigrateCommand migrateCommand = new AutoFaker<CreativeMigrateCommand>()
            .RuleFor(migrateCommand => migrateCommand.CreativeTemplateId, CreativeTemplateId)
            .RuleFor(migrateCommand => migrateCommand.AdBookClientId, faker => faker.Random.Long(min: 1, max: 100000))
            .RuleFor(migrateCommand => migrateCommand.CampaignId, faker => faker.Random.Long(min: 1, max: 100000))
            .RuleFor(migrateCommand => migrateCommand.AdBookAdId, faker => faker.Random.Long(min: 1, max: 100000))
            .RuleFor(migrateCommand => migrateCommand.LineItemIds, new HashSet<long> { 1, 2, 3 })
            .Generate();

        _creativeTemplateRepositoryMock
            .Setup(creativeTemplateRepository =>
                creativeTemplateRepository.FindByIdAsync(It.IsAny<CreativeTemplateId>()))
            .ReturnsAsync(creativeTemplate);

        _creativeRepositoryMock.Setup(creativeRepository =>
                creativeRepository.FindMigratedAdBookCreativeAsync(It.IsAny<long>(), It.IsAny<long>(),
                    It.IsAny<long>()))
            .ReturnsAsync(Result.Fail(new EntityNotFoundError(migrateCommand.AdBookAdId.ToString(), nameof(CreativeFile))));

        Result<CreativeResult> result = await _creativeMigrateHandler.Handle(migrateCommand, CancellationToken.None);

        result.Should().BeSuccess();
        _creativeRepositoryMock.Verify(
            creativeRepository => creativeRepository.FindMigratedAdBookCreativeAsync(It.IsAny<long>(), It.IsAny<long>(), It.IsAny<long>()),
            Times.Once);
        _creativeRepositoryMock.Verify(
            creativeRepository => creativeRepository.MigrateAsync<CreativeResult>(It.IsAny<Creative>()),
            Times.Once);
    }
}