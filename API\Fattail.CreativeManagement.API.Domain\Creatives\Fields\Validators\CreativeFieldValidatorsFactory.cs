﻿using Fattail.CreativeManagement.API.Domain.Creatives.Fields.Validators.RequiredValidators;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates.Fields;

namespace Fattail.CreativeManagement.API.Domain.Creatives.Fields.Validators;

public static class CreativeFieldValidatorsFactory
{
    public static IReadOnlyList<IRuleValidator<TFieldValue>> GetValidators<TFieldValue> (IEnumerable<ValidationRule> validationRules, CreativeFieldIdentifier creativeFieldIdentifier)
    {
        var validators = new List<IRuleValidator<TFieldValue>>();

        foreach (ValidationRule validationRule in validationRules)
        {
            switch (validationRule.Type)
            {
                case CreativeFieldValidationRuleType.FileUploadExtensions:
                    validators.Add((IRuleValidator<TFieldValue>)new FileUploadExtensionsRuleValidator(validationRules, creativeFieldIdentifier));
                    break;
                case CreativeFieldValidationRuleType.FileSize:
                    validators.Add((IRuleValidator<TFieldValue>)new FileSizeRuleValidator(validationRules, creativeFieldIdentifier));
                    break;
                case CreativeFieldValidationRuleType.Required:
                    validators.Add(RequiredRuleValidatorsFactory.GetValidator<TFieldValue>(creativeFieldIdentifier));
                    break;
                default:
                    throw new NotImplementedException();
            }
        }

        return validators;
    }
}