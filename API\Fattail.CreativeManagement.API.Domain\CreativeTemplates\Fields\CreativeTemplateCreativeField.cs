﻿using Fattail.CreativeManagement.API.Domain.Common;
using Fattail.CreativeManagement.API.Domain.Common.Errors;
using Fattail.CreativeManagement.API.Domain.CreativeFields;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates.Errors;
using FluentResults;

namespace Fattail.CreativeManagement.API.Domain.CreativeTemplates.Fields
{
    public class CreativeTemplateCreativeField : Entity<CreativeFieldId>
    {
        protected CreativeTemplateCreativeField (
            CreativeFieldId id,
            string name,
            CreativeFieldType type,
            Dictionary<CreativeFieldValidationRuleType, ValidationRule> validationRules,
            int displayOrder,
            string? tooltip) :
            base(id)
        {
            Name = name;
            Type = type;
            DisplayOrder = displayOrder;
            _validationRules = validationRules;
            Tooltip = tooltip;
        }
        
        public string Name { get;  }

        public CreativeFieldType Type { get; }
        
        public int DisplayOrder { get; private set; }

        private readonly Dictionary<CreativeFieldValidationRuleType, ValidationRule> _validationRules;

        public IReadOnlyList<ValidationRule> ValidationRules => _validationRules.Values.ToList().AsReadOnly();

        public string? Tooltip { get; private set; }

        public static Result<CreativeTemplateCreativeField> Create (CreativeFieldId? creativeFieldId, IReadOnlySet<CreativeField> existingCreativeFields, int order, IReadOnlyDictionary<SpecialPropertyKey, object?>? specialProperties = null)
        {
            if (creativeFieldId == null || creativeFieldId == CreativeFieldId.Transient)
            {
                return Result.Fail(new RequiredValueMissingError($"creative field id", 
                    nameof(CreativeTemplateCreativeField)));
            }
            
            CreativeField? creativeField = existingCreativeFields.FirstOrDefault(ecf => ecf.Id == creativeFieldId);
            
            if (creativeField is null)
            {
                return Result.Fail(new InvalidValueError($"creative field id: {creativeFieldId}",
                    nameof(CreativeTemplateCreativeField)));
            }

            return creativeField.Type switch
            {
                CreativeFieldType.SingleLineText 
                    or CreativeFieldType.MultiFileUpload 
                    or CreativeFieldType.FileUpload
                    or CreativeFieldType.MultiLineText => new CreativeTemplateCreativeField(
                        creativeFieldId, 
                        creativeField.Name, 
                        creativeField.Type, 
                        new Dictionary<CreativeFieldValidationRuleType, 
                            ValidationRule>(), 
                        order, 
                        null),
                CreativeFieldType.MultiSelectOption => new CreativeTemplateMultiSelectCreativeField(
                    creativeFieldId, 
                    creativeField.Name, 
                    creativeField.Type,
                    ((MultiSelectCreativeField)creativeField).Options,
                    new Dictionary<CreativeFieldValidationRuleType, ValidationRule>(), 
                    order, 
                    null),
                CreativeFieldType.SingleSelectOption => new CreativeTemplateSingleSelectCreativeField(
                    creativeFieldId, 
                    creativeField.Name, 
                    creativeField.Type,
                    ((SingleSelectCreativeField)creativeField).Options,
                    new Dictionary<CreativeFieldValidationRuleType, ValidationRule>(), 
                    order, 
                    null),
                CreativeFieldType.SectionDivider => CreativeTemplateSectionDividerCreativeField.Create(creativeFieldId, creativeField.Name, creativeField.Type, order, null, specialProperties),
                _ => throw new ArgumentOutOfRangeException(nameof(CreativeTemplateCreativeField), creativeField.Type, null),
            };
        }

        public Result AddValidationRule (CreativeFieldValidationRuleType validationRuleType,
            IReadOnlyList<string> options)
        {
            var result = new Result();
            var creativeFieldValidationRulePolicy = new CreativeFieldValidationRulePolicy(Type);

            if (!creativeFieldValidationRulePolicy.Supports(validationRuleType))
            {
                return Result.Merge(result, Result.Fail(new CreativeTemplateUnsupportedValidationTypeError(Id.ToString(),
                    nameof(CreativeField), validationRuleType.ToString())));
            }
            
            if (_validationRules.ContainsKey(validationRuleType))
            {
                return Result.Merge(result, Result.Fail(new CreativeTemplateDuplicateValidationRuleError(Id,
                    nameof(CreativeField), validationRuleType)));
            }

            Result<ValidationRule> validationRuleResult = ValidationRule.Create(validationRuleType, options, Id);

            result = Result.Merge(result, validationRuleResult.ToResult());

            if (result.IsFailed)
            {
                return result;
            }

            _validationRules.Add(validationRuleType, validationRuleResult.Value);

            return Result.Ok();
        }

        public void SetTooltip(string? tooltip)
        {
            Tooltip = tooltip;
        }

        public virtual Result SetSpecialPropertiesValues (IReadOnlyDictionary<SpecialPropertyKey, object?> specialProperties)
        {
            return Result.Fail(new CreativeTemplateFieldSpecialPropertiesNotSupportedError(Id, nameof(CreativeTemplateCreativeField)));
        }
    }
}