﻿using AutoMapper;
using Fattail.CreativeManagement.API.Application.CreativeFields;
using Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Entities;
using CreativeFieldDomain = Fattail.CreativeManagement.API.Domain.CreativeFields.CreativeField;
using MultiSelectCreativeFieldDomain = Fattail.CreativeManagement.API.Domain.CreativeFields.MultiSelectCreativeField;
using SingleSelectCreativeFieldDomain = Fattail.CreativeManagement.API.Domain.CreativeFields.SingleSelectCreativeField;
using SelectOptionDomain = Fattail.CreativeManagement.API.Domain.CreativeFields.SelectOption;

namespace Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Configuration.AutoMapper
{
    internal class CreativeFieldProfile : Profile
    {
        public CreativeFieldProfile() 
        {
            CreateMap<CreativeField, CreativeFieldDomain>()
                .Include(typeof(MultiSelectCreativeField), typeof(MultiSelectCreativeFieldDomain))
                .Include(typeof(SingleSelectCreativeField), typeof(SingleSelectCreativeFieldDomain)).ReverseMap();

            CreateMap<MultiSelectCreativeField, MultiSelectCreativeFieldDomain>().ReverseMap();
            CreateMap<SingleSelectCreativeField, SingleSelectCreativeFieldDomain>().ReverseMap();
            CreateMap<SelectOption, SelectOptionDomain>().ReverseMap();

            CreateMap<CreativeField, CreativeFieldResult>()
                .Include(typeof(MultiSelectCreativeField), typeof(MultiSelectCreativeFieldResult))
                .Include(typeof(SingleSelectCreativeField), typeof(SingleSelectCreativeFieldResult));

            CreateMap<MultiSelectCreativeField, MultiSelectCreativeFieldResult>();
            CreateMap<SingleSelectCreativeField, SingleSelectCreativeFieldResult>();
            CreateMap<SelectOption, SelectOptionResult>();
        }
    }
}
