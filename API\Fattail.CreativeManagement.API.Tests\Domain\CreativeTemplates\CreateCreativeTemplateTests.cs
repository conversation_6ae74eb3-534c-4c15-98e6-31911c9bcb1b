﻿using Fattail.CreativeManagement.API.Domain.Common;
using Fattail.CreativeManagement.API.Domain.Common.Errors;
using Fattail.CreativeManagement.API.Domain.CreativeFields;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates.Errors;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates.Factory;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates.Requirements;
using FluentAssertions;
using FluentResults;
using FluentResults.Extensions.FluentAssertions;
using Moq;
using NUnit.Framework;

namespace Fattail.CreativeManagement.API.Tests.Domain.CreativeTemplates;

[TestFixture]
public class CreateCreativeTemplateTests
{
    [SetUp]
    public void SetUp ()
    {
        _idManagerMock = new Mock<IIdManager>();
        _idManagerMock.Setup(idManager => idManager.GetId()).Returns(1234);
    }

    private Mock<IIdManager> _idManagerMock = null!;
    private readonly CreativeFieldUniqueNameRequirement _creativeFieldUniqueNameRequirement = new(true);
    private readonly string _fieldName = "fieldName";

    [Test]
    public async Task Creative_template_cant_be_created_without_creative_fields ()
    {
        var createCreativeTemplateRequest = new CreativeTemplateCreateRequest(
            new CreativeTemplateId(1234),
            CreativeTemplateName.Create("creative template name", new CreativeTemplateUniqueNameRequirement(true)).Value,
            CreativeType.Undefined,
            new List<DisplayOrderCreativeField>(),
            new HashSet<CreativeField>([ CreativeField.Create(new(123), "name", CreativeFieldType.SingleLineText, _creativeFieldUniqueNameRequirement, false, null).Value ]),
            false
        );

        Result<CreativeTemplate> creativeTemplateResult = CreativeTemplateFactory.Create(createCreativeTemplateRequest);

        creativeTemplateResult.Should().BeFailure()
            .And.HaveReason(new RequiredValueMissingError("creative template fields", nameof(CreativeTemplate)));
    }

    [Test]
    public async Task Creative_template_cant_be_created_with_duplicate_fields ()
    {
        string creativeTemplateName = "Creative template name";

        var createCreativeTemplateRequest = new CreativeTemplateCreateRequest(
            new CreativeTemplateId(1234),
            CreativeTemplateName.Create(creativeTemplateName, new CreativeTemplateUniqueNameRequirement(true)).Value,
            CreativeType.Undefined,
            [new(new(123), 1), new(new(456), 2), new(new(456), 3)],
            new HashSet<CreativeField>(
            [
                CreativeField.Create(new (123), _fieldName, It.IsAny<CreativeFieldType>(), _creativeFieldUniqueNameRequirement, false,null).Value,
                CreativeField.Create(new (456), _fieldName, It.IsAny<CreativeFieldType>(), _creativeFieldUniqueNameRequirement, false, null).Value,
                CreativeField.Create(new (456), _fieldName, It.IsAny<CreativeFieldType>(), _creativeFieldUniqueNameRequirement, false, null).Value
            ]),
            false
        );

        Result<CreativeTemplate> creativeTemplateResult = CreativeTemplateFactory.Create(createCreativeTemplateRequest);

        creativeTemplateResult.Should().BeFailure()
            .And.HaveReason(new CreativeTemplateDuplicateFieldError(new CreativeFieldId(456), nameof(CreativeField)));
        creativeTemplateResult.Errors.Should().ContainSingle();
    }

    [Test]
    public async Task Creative_template_cant_be_created_with_duplicate_field_order_values ()
    {
        string creativeTemplateName = "Creative template name";

        _idManagerMock.SetupSequence(idManager => idManager.GetId()).Returns(123).Returns(456);

        var createCreativeTemplateRequest = new CreativeTemplateCreateRequest(
            new CreativeTemplateId(1234),
            CreativeTemplateName.Create(creativeTemplateName, new CreativeTemplateUniqueNameRequirement(true)).Value,
            CreativeType.Undefined,
            [new(new(123), 1), new(new(456), 1)],
            new HashSet<CreativeField>(
            [
                CreativeField.Create(new(123), _fieldName, It.IsAny<CreativeFieldType>(), _creativeFieldUniqueNameRequirement, false, null).Value,
                CreativeField.Create(new(456), _fieldName, It.IsAny<CreativeFieldType>(), _creativeFieldUniqueNameRequirement, false, null).Value
            ]),
            false
        );

        Result<CreativeTemplate> creativeTemplateResult = CreativeTemplateFactory.Create(createCreativeTemplateRequest);

        creativeTemplateResult.Should().BeFailure()
            .And.HaveReason(new CreativeTemplateDuplicateDisplayOrderError(nameof(CreativeField)));
        creativeTemplateResult.Errors.Should().ContainSingle();
    }

    [Test]
    public async Task Creative_template_cant_be_created_when_not_all_fields_contain_display_order_property ()
    {
        string creativeTemplateName = "Creative template name";

        _idManagerMock.SetupSequence(idManager => idManager.GetId()).Returns(123).Returns(456).Returns(789);

        var createCreativeTemplateRequest = new CreativeTemplateCreateRequest(
            new CreativeTemplateId(1234),
            CreativeTemplateName.Create(creativeTemplateName, new CreativeTemplateUniqueNameRequirement(true)).Value,
            CreativeType.Undefined,
            [new(new(123), 1), new(new(456), 2), new(new(789), null)],
            new HashSet<CreativeField>(
            [
                CreativeField.Create(new(123), _fieldName, It.IsAny<CreativeFieldType>(), _creativeFieldUniqueNameRequirement, false, null).Value,
                CreativeField.Create(new(456), _fieldName, It.IsAny<CreativeFieldType>(), _creativeFieldUniqueNameRequirement, false, null).Value,
                CreativeField.Create(new(789), _fieldName, It.IsAny<CreativeFieldType>(), _creativeFieldUniqueNameRequirement, false, null).Value
            ]),
            false
        );

        Result<CreativeTemplate> creativeTemplateResult = CreativeTemplateFactory.Create(createCreativeTemplateRequest);

        creativeTemplateResult.Should().BeFailure()
            .And.HaveReason(new CreativeTemplateMissingDisplayOrderPropertyError("789", "CreativeField"));
        creativeTemplateResult.Errors.Should().ContainSingle();
    }

    [Test]
    public async Task Creative_template_can_be_created_with_incremental_display_order_values_for_each_field ()
    {
        string creativeTemplateName = "Creative template name";

        _idManagerMock.SetupSequence(idManager => idManager.GetId()).Returns(123).Returns(456).Returns(789);

        var createCreativeTemplateRequest = new CreativeTemplateCreateRequest(
            new CreativeTemplateId(1234),
            CreativeTemplateName.Create(creativeTemplateName, new CreativeTemplateUniqueNameRequirement(true)).Value,
            CreativeType.Undefined,
            [new(new(123), null), new(new(456), null), new(new(789), null)],
            new HashSet<CreativeField>(
            [
                CreativeField.Create(new(123), _fieldName, It.IsAny<CreativeFieldType>(), _creativeFieldUniqueNameRequirement, false, null).Value,
                CreativeField.Create(new(456), _fieldName, It.IsAny<CreativeFieldType>(), _creativeFieldUniqueNameRequirement, false, null).Value,
                CreativeField.Create(new(789), _fieldName, It.IsAny<CreativeFieldType>(), _creativeFieldUniqueNameRequirement, false, null).Value
            ]),
            false
        );

        Result<CreativeTemplate> creativeTemplateResult = CreativeTemplateFactory.Create(createCreativeTemplateRequest);

        creativeTemplateResult.Should().BeSuccess()
            .And.Satisfy(creativeTemplate =>
            {
                creativeTemplate.Value.CreativeFields.Should().HaveCount(3);
                creativeTemplate.Value.CreativeFields.Should().Contain(field => field.DisplayOrder == 0);
                creativeTemplate.Value.CreativeFields.Should().Contain(field => field.DisplayOrder == 1);
                creativeTemplate.Value.CreativeFields.Should().Contain(field => field.DisplayOrder == 2);
            });
    }

    [Test]
    public async Task Creative_template_can_be_created_with_order_provided ()
    {
        string creativeTemplateName = "Creative template name";

        _idManagerMock.SetupSequence(idManager => idManager.GetId()).Returns(123).Returns(456).Returns(789);

        var createCreativeTemplateRequest = new CreativeTemplateCreateRequest(
            new CreativeTemplateId(1234),
            CreativeTemplateName.Create(creativeTemplateName, new CreativeTemplateUniqueNameRequirement(true)).Value,
            CreativeType.Undefined,
            [new(new(123), 10), new(new(456), 15), new(new(789), 20)],
            new HashSet<CreativeField>(
            [
                CreativeField.Create(new(123), _fieldName, It.IsAny<CreativeFieldType>(), _creativeFieldUniqueNameRequirement, false, null).Value,
                CreativeField.Create(new(456), _fieldName, It.IsAny<CreativeFieldType>(), _creativeFieldUniqueNameRequirement, false, null).Value,
                CreativeField.Create(new(789), _fieldName, It.IsAny<CreativeFieldType>(), _creativeFieldUniqueNameRequirement, false, null).Value
            ]),
            false
        );

        Result<CreativeTemplate> creativeTemplateResult = CreativeTemplateFactory.Create(createCreativeTemplateRequest);

        creativeTemplateResult.Should().BeSuccess()
            .And.Satisfy(creativeTemplate =>
            {
                creativeTemplate.Value.CreativeFields.Should().HaveCount(3);
                creativeTemplate.Value.CreativeFields.Should().Contain(field => field.DisplayOrder == 10);
                creativeTemplate.Value.CreativeFields.Should().Contain(field => field.DisplayOrder == 15);
                creativeTemplate.Value.CreativeFields.Should().Contain(field => field.DisplayOrder == 20);
            });
    }

    [Test]
    public async Task Creative_template_can_be_created ()
    {
        _idManagerMock.SetupSequence(idManager => idManager.GetId()).Returns(123).Returns(456);

        CreativeTemplateName creativeTemplateName = CreativeTemplateName.Create("Creative template name", new CreativeTemplateUniqueNameRequirement(true)).Value;

        var createCreativeTemplateRequest = new CreativeTemplateCreateRequest(
            new CreativeTemplateId(1234),
            creativeTemplateName,
            CreativeType.Undefined,
            [new(new(123), 1), new(new(456), 2)],
            new HashSet<CreativeField>(
            [
                CreativeField.Create(new (123), _fieldName, CreativeFieldType.SingleLineText, _creativeFieldUniqueNameRequirement, false, null).Value,
                CreativeField.Create(new (456), _fieldName, CreativeFieldType.SingleLineText, _creativeFieldUniqueNameRequirement, false, null).Value
            ]),
            false
        );

        Result<CreativeTemplate> creativeTemplateResult = CreativeTemplateFactory.Create(createCreativeTemplateRequest);

        creativeTemplateResult.Should().BeSuccess();
        
        CreativeTemplate creativeTemplate = creativeTemplateResult.Value;
        
        creativeTemplate.Should().NotBeNull();
        creativeTemplate.Name.Should().Be(creativeTemplateName);
        creativeTemplate.CreativeType.Should().Be(CreativeType.Undefined);
        creativeTemplate.CreativeFields.Should().HaveCount(2);
        creativeTemplate.CreativeFields.Should().Contain(field => field.Id == new CreativeFieldId(123));
        creativeTemplate.CreativeFields.Should().Contain(field => field.Id == new CreativeFieldId(456));
        creativeTemplate.Predefined.Should().BeFalse();
    }
}