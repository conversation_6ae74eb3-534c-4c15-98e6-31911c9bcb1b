﻿using Azure;
using Azure.Storage.Blobs;
using Azure.Storage.Blobs.Models;
using Fattail.CreativeManagement.API.Application.CreativeFiles.CreativeFilesGenerateZip.Errors;
using Fattail.CreativeManagement.API.Application.CreativeFiles.CreativeFilesUpload.Errors;
using Fattail.CreativeManagement.API.Domain.CreativeFiles;
using FluentResults;
using Microsoft.Extensions.Logging;
using System.IO.Compression;
using System.Net;
using Fattail.CreativeManagement.API.Application.CreativeFiles.CreativeFilesUpload;
using Fattail.CreativeManagement.API.Application.CreativeFiles.CreativeFilesGenerateZip;
using Fattail.CreativeManagement.API.Application;
using Fattail.CreativeManagement.API.Application.CreativeFiles.CreativeFilesMigrate.Errors;
using Fattail.CreativeManagement.API.Domain.CreativeFiles.Errors;
using Fattail.CreativeManagement.API.Infrastructure.Settings;
using Microsoft.Extensions.Options;

namespace Fattail.CreativeManagement.API.Infrastructure.CreativeFilesManager;

internal sealed class CreativeFileManager : ICreativeFileStorageManager, ICreativeFilesZipGenerator
{
    private readonly ILogger<CreativeFileManager> _logger;
    private readonly BlobContainerClient _azureBlobContainerClient;
    private readonly BlobContainerClient _azureZipBlobContainerClient;
    private readonly IOrganizationContext _organizationContext;
    private readonly AdBookCreativeSettings _adBookCreativeSettings;
    private readonly HttpClient _client;
    private readonly ParallelExecutionSettings _parallelExecutionSettings;

    public CreativeFileManager (
        IOrganizationContext organizationContext,
        AzureBlobContainerClientManager azureBlobStorageSettings,
        IOptions<AdBookCreativeSettings> adBookCreativeSettings,
        IOptions<ParallelExecutionSettings> parallelExecutionSettings,
        HttpClient client,
        ILogger<CreativeFileManager> logger)
    {
        _logger = logger;
        _organizationContext = organizationContext;
        _adBookCreativeSettings = adBookCreativeSettings.Value;
        _client = client;
        _parallelExecutionSettings = parallelExecutionSettings.Value;

        if (azureBlobStorageSettings.ContainerClient.TryGetValue(organizationContext.OrganizationId,
                out ContainerClients? containerClient))
        {
            _azureBlobContainerClient = containerClient.CreativeContainerClient.Value;
            _azureZipBlobContainerClient = containerClient.ZipCreativeContainerClient.Value;
        }
        else
        {
            _azureBlobContainerClient = azureBlobStorageSettings.ContainerClient[0]
                .CreativeContainerClient.Value;
            _azureZipBlobContainerClient = azureBlobStorageSettings.ContainerClient[0]
                .ZipCreativeContainerClient.Value;
        }
    }

    public async Task<Result<CreativeFileStorageMetadata>> StoreCreativeFile (
        CreativeFileName creativeFileName, Stream content)
    {
        string blobName = $"{_organizationContext.OrganizationId}/{creativeFileName.NameWithoutExtension}_{Guid.NewGuid()}{creativeFileName.Extension}";
        BlobClient blobClient = _azureBlobContainerClient.GetBlobClient(blobName);
        Response<BlobContentInfo> uploadResponse;

        try
        {
            uploadResponse = await blobClient.UploadAsync(content);
        }
        catch (RequestFailedException ex)
        {
            _logger.LogError(ex, @"An error has occurred while trying to upload creative file ""{creativeFileName}"" with blob name ""{blobName}""", creativeFileName, blobName);
            return Result.Fail(new CreativeFileStorageError(creativeFileName.Name));
        }

        return new CreativeFileStorageMetadata(
            blobName,
            WebUtility.UrlDecode(blobClient.Uri.AbsoluteUri),
            uploadResponse.Value.LastModified.UtcDateTime);
    }

    public async Task<Result<Tuple<CreativeFileStorageMetadata, Stream>>> StoreAbOmsCreativeFile (CreativeFileName creativeFileName, string fileLocation)
    {
        string blobName =
            $"{_organizationContext.OrganizationId}/{creativeFileName.NameWithoutExtension}_{Guid.NewGuid()}{creativeFileName.Extension}";
        BlobClient blobClient = _azureBlobContainerClient.GetBlobClient(blobName);

        string creativesBaseUri = _adBookCreativeSettings.GetAdBookCreativeBaseUri(_organizationContext.OrganizationId);

        Response<BlobContentInfo> uploadResponse;
        string fileUrl = Path.Combine(creativesBaseUri, fileLocation);

        MemoryStream fileStream;

        try
        {
            using HttpResponseMessage fileResponse = await _client.GetAsync(fileUrl);
            if (fileResponse.StatusCode == HttpStatusCode.NotFound)
            {
                return Result.Fail(new CreativeFileNotFoundInAbOmsError(creativeFileName.Name, fileLocation));
            }
            fileStream = new MemoryStream();
            await fileResponse.Content.CopyToAsync(fileStream);
        }
        catch (HttpRequestException ex)
        {
            return Result.Fail(new CreativeFileNotFoundInAbOmsError(creativeFileName.Name, fileLocation));
        }

        try
        {
            fileStream.Position = 0;
            uploadResponse = await blobClient.UploadAsync(fileStream);
        }
        catch (RequestFailedException ex)
        {
            _logger.LogError(ex,
                @"An error has occurred while trying to upload creative file ""{creativeFileName}"" with blob name ""{blobName}""",
                creativeFileName, blobName);
            return Result.Fail(new CreativeFileStorageError(creativeFileName.Name));
        }

        fileStream.Position = 0;
        return new Tuple<CreativeFileStorageMetadata, Stream>(
            new CreativeFileStorageMetadata(
                blobName,
                WebUtility.UrlDecode(blobClient.Uri.AbsoluteUri),
                uploadResponse.Value.LastModified.UtcDateTime), fileStream);
    }

    public async Task<Result> DeleteOrganizationVirtualFolder ()
    {
        Pageable<BlobItem>? blobs = _azureBlobContainerClient.GetBlobs();

        await Parallel.ForEachAsync(blobs, new ParallelOptions { MaxDegreeOfParallelism = _parallelExecutionSettings.MaxDegreeOfParallelismForDeletion }, async (blob, _) =>
        {
            if (blob.Name.StartsWith(_organizationContext.OrganizationId.ToString()))
            {
                BlobClient blobClient = _azureBlobContainerClient.GetBlobClient(blob.Name);
                await blobClient.DeleteIfExistsAsync();
            }
        });

        return Result.Ok();
    }

    public async Task<long> GetFileSizeInBytesAsync (string blobName)
    {
        try
        {
            BlobClient blobClient = _azureBlobContainerClient.GetBlobClient(blobName);
            Response<BlobProperties> blobProperties = await blobClient.GetPropertiesAsync();
            return blobProperties.Value.ContentLength;
        }
        catch
        {
            return 0;
        }
    }

    public async Task<Result<CreativeFilesGenerateZipResult>> GenerateTemporalCreativeFileZip (
        IEnumerable<CreativeFile> creativeFiles)
    {
        string zipBlobName = $"{Guid.NewGuid()}.zip";
        BlobClient zipBlobClient = _azureZipBlobContainerClient.GetBlobClient(zipBlobName);

        IEnumerable<CreativeFile> creativeFilesToZip = creativeFiles as CreativeFile[] ?? creativeFiles.ToArray();
        try
        {
            await using (Stream zipBlobStream = await zipBlobClient.OpenWriteAsync(true))
            {
                using (var zipArchive = new ZipArchive(zipBlobStream, ZipArchiveMode.Create))
                {
                    foreach (CreativeFile creativeFile in creativeFilesToZip)
                    {
                        BlobClient? blobClient =
                            _azureBlobContainerClient.GetBlobClient(creativeFile.StorageMetadata!.BlobName);

                        if (blobClient.Exists())
                        {
                            ZipArchiveEntry creativeFileEntry =
                                zipArchive.CreateEntry(creativeFile.Name.Name, CompressionLevel.Optimal);
                            using (Stream innerFile = creativeFileEntry.Open())
                            {
                                await blobClient.DownloadToAsync(innerFile);
                            }
                        }
                    }
                }
            }
        }
        catch (RequestFailedException ex)
        {
            _logger.LogError(ex, @"An error occurred while trying to generate creative files ""{creativeFiles}"" zip with zip blob name ""{zipBlobName}""",
                string.Join(",", creativeFilesToZip.Select(creativeFile => creativeFile.Id.ToString())),
                zipBlobName);
            return Result.Fail(new CreativeFilesToDownloadError());
        }

        return new CreativeFilesGenerateZipResult(
            WebUtility.UrlDecode(zipBlobClient.Uri.AbsoluteUri));
    }

    public async Task<Result> DeleteCreativeFile (string blobName)
    {
        BlobClient blobClient = _azureBlobContainerClient.GetBlobClient(blobName);

        try
        {
            await blobClient.DeleteIfExistsAsync();
        }
        catch (RequestFailedException ex)
        {
            _logger.LogError(ex, @"An error occurred while trying to delete creative file with blob name ""{blobName}""", blobClient.Name);
            return Result.Fail(new CreativeFileDeleteError(blobClient.Name));
        }

        return Result.Ok();
    }
}