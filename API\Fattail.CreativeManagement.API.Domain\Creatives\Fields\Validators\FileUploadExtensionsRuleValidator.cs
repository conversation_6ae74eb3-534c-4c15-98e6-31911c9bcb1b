﻿using Fattail.CreativeManagement.API.Domain.Common.Errors;
using Fattail.CreativeManagement.API.Domain.CreativeFiles;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates.Fields;
using FluentResults;

namespace Fattail.CreativeManagement.API.Domain.Creatives.Fields.Validators
{
    public class FileUploadExtensionsRuleValidator : IRuleValidator<IReadOnlyList<CreativeFile?>>
    {
        private readonly string _creativeFieldId;
        public FileUploadExtensionsRuleValidator (IEnumerable<ValidationRule> validationRules, CreativeFieldIdentifier creativeFieldIdentifier)
        {
            ValidationRule? fileExtensionsValidationRule = validationRules.FirstOrDefault(validationRule =>
                validationRule.Type == CreativeFieldValidationRuleType.FileUploadExtensions);

            _allowedExtensions = fileExtensionsValidationRule?.Options.Select(option => option.ToUpper()).ToList() ??
                                 new List<string>();

            _creativeFieldId = creativeFieldIdentifier.Id.ToString();
        }

        private readonly IReadOnlyList<string> _allowedExtensions;
        private string AllowedExtensionsAsString => string.Join(", ", _allowedExtensions);

        private bool CreativeFileExtensionIsValid (CreativeFile creativeFile)
        {
            return _allowedExtensions.Contains(creativeFile.Name.Extension.ToString().ToUpper());
        }

        public Result IsValid (IReadOnlyList<CreativeFile?> creativeFiles)
        {
            var result = Result.Ok();

            if (creativeFiles != null && creativeFiles.Any() && _allowedExtensions.Any())
            {
                foreach (CreativeFile? creativeFile in creativeFiles)
                {
                    if (creativeFile != null && !CreativeFileExtensionIsValid(creativeFile))
                    {
                        result = Result.Merge(result, Result.Fail(new FileUploadExtensionsValidationError(nameof(CreativeFieldValue), nameof(Creative), _creativeFieldId, AllowedExtensionsAsString)));
                        break;
                    }
                }
            }

            return result;
        }
    }
}