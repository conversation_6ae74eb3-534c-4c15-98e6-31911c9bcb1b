﻿using Fattail.CreativeManagement.API.Application;
using Fattail.CreativeManagement.API.Domain.CreativeFields;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates;
using Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Entities;
using Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Interfaces;
using Microsoft.Azure.Cosmos;
using Microsoft.Azure.Cosmos.Linq;
using CreativeTemplate = Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Entities.CreativeTemplate;

namespace Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Migrations;

public class CreativeTemplatesDataMigration : ICreativeTemplatesDataMigration
{
    private readonly ICosmosDbContainerFactory _cosmosDbContainerFactory;
    private readonly string _containerName = "CreativeTemplates";
    private readonly ICosmosDbContainer _container;
    protected readonly IOrganizationContext _organizationContext;

    public CreativeTemplatesDataMigration (ICosmosDbContainerFactory cosmosDbContainerFactory, IOrganizationContext organizationContext)
    {
        _cosmosDbContainerFactory = cosmosDbContainerFactory;
        _container = _cosmosDbContainerFactory.GetContainer(_containerName);
        _organizationContext = organizationContext;
    }

    public async Task RunMigrationAsync ()
    {
        await _cosmosDbContainerFactory.EnsureDbSetupAsync();

        List<CreativeTemplate> templates = await GetAllCreativeTemplates();
        foreach (CreativeTemplate template in templates)
        {
            if (!template.CreativeFields.Any() || template.OrgId != _organizationContext.OrganizationId.ToString())
            {
                continue;
            }

            foreach (CreativeTemplateCreativeField creativeTemplateCreativeField in template.CreativeFields)
            {
                if (creativeTemplateCreativeField.ValidationRules != null &&
                    creativeTemplateCreativeField.ValidationRules.Any())
                {
                    continue;
                }

                if (creativeTemplateCreativeField.Type is CreativeFieldType.FileUpload
                    or CreativeFieldType.MultiFileUpload)
                {
                    creativeTemplateCreativeField.ValidationRules = new[]
                    {
                        new ValidationRule
                        {
                            Type = CreativeFieldValidationRuleType.FileUploadExtensions,
                            Options = new[]
                            {
                                ".png",
                                ".svg",
                                ".jpeg",
                                ".gif",
                                ".pdf",
                                ".mp4",
                                ".xlsx",
                                ".xls",
                                ".doc",
                                ".psd",
                                ".txt",
                                ".zip",
                                ".jpg",
                                ".docx",
                                ".ppt",
                                ".pptx"
                            }
                        }
                    };
                }
                else
                {
                    creativeTemplateCreativeField.ValidationRules = Array.Empty<ValidationRule>();
                }
            }
        }

        foreach (CreativeTemplate template in templates)
        {
            await _container.Container.ReplaceItemAsync(template, template.Id,
                new PartitionKey(template.OrgId));
        }
    }

    private async Task<List<CreativeTemplate>> GetAllCreativeTemplates ()
    {
        var items = new List<CreativeTemplate>();
        IQueryable<CreativeTemplate> dbQuery = _container.Container.GetItemLinqQueryable<CreativeTemplate>(requestOptions: new QueryRequestOptions()
            {
                PartitionKey = new PartitionKey($"{_organizationContext.OrganizationId}")
            });

        using var resultSet = dbQuery.ToFeedIterator();
        while (resultSet.HasMoreResults)
        {
            FeedResponse<CreativeTemplate> response = await resultSet.ReadNextAsync();
            items.AddRange(response.Resource);
        }

        return items;
    }
}