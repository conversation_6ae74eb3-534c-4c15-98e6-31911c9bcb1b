﻿using Fattail.CreativeManagement.API.Domain.Common.Errors;
using FluentResults;

namespace Fattail.CreativeManagement.API.Domain.Creatives.Fields.Validators.RequiredValidators;

public class StringRequiredRuleValidator : IRuleValidator<string>
{
    private readonly string _creativeFieldId;

    public StringRequiredRuleValidator (CreativeFieldIdentifier creativeFieldIdentifier)
    {
        _creativeFieldId = creativeFieldIdentifier.Id.ToString();
    }

    public Result IsValid (string fieldValue)
    {
        var result = Result.Ok();

        if (string.IsNullOrEmpty(fieldValue) || string.IsNullOrWhiteSpace(fieldValue))
        {
            result = Result.Fail(new RequiredValidationError(nameof(CreativeFieldValue), nameof(Creative),
                _creativeFieldId));
        }

        return result;
    }
}