﻿using Fattail.CreativeManagement.API.Domain.CreativeFields;
using JsonSubTypes;
using Newtonsoft.Json;

namespace Fattail.CreativeManagement.API.Azure.Endpoints.Creatives.Common;

[JsonConverter(typeof(JsonSubtypes), "Type")]
[JsonSubtypes.KnownSubTypeAttribute(typeof(MultiFileUploadFieldValue), CreativeFieldType.MultiFileUpload)]
[JsonSubtypes.KnownSubTypeAttribute(typeof(FileUploadFieldValue), CreativeFieldType.FileUpload)]
[JsonSubtypes.KnownSubTypeAttribute(typeof(SingleLineTextFieldValue), CreativeFieldType.SingleLineText)]
[JsonSubtypes.KnownSubTypeAttribute(typeof(MultiSelectOptionFieldValue), CreativeFieldType.MultiSelectOption)]
[JsonSubtypes.KnownSubTypeAttribute(typeof(SingleSelectOptionFieldValue), CreativeFieldType.SingleSelectOption)]
[JsonSubtypes.KnownSubTypeAttribute(typeof(MultiLineTextFieldValue), CreativeFieldType.MultiLineText)]
internal abstract record CreativeFieldRequest(
    long Id,
    CreativeFieldType Type);

internal abstract record CreativeFieldValue<TValue>(
        long Id,
        CreativeFieldType Type,
        TValue Value)
    : CreativeFieldRequest(Id, Type);

internal sealed record MultiFileUploadFieldValue(
        long Id,
        IReadOnlyList<long> Value)
    : CreativeFieldValue<IReadOnlyList<long>>(Id, CreativeFieldType.MultiFileUpload, Value);

internal sealed record FileUploadFieldValue(
        long Id,
        long Value)
    : CreativeFieldValue<long>(Id, CreativeFieldType.FileUpload, Value);

internal sealed record SingleLineTextFieldValue(
        long Id,
        string Value)
    : CreativeFieldValue<string>(Id, CreativeFieldType.SingleLineText, Value);

internal sealed record MultiSelectOptionFieldValue (
        long Id,
        IReadOnlyList<long> Value)
    : CreativeFieldValue<IReadOnlyList<long>>(Id, CreativeFieldType.MultiSelectOption, Value);

internal sealed record SingleSelectOptionFieldValue (
    long Id,
    long? Value) : CreativeFieldValue<long?>(Id, CreativeFieldType.SingleSelectOption, Value);
    
internal sealed record MultiLineTextFieldValue(
        long Id,
        string Value)
    : CreativeFieldValue<string>(Id, CreativeFieldType.MultiLineText, Value);