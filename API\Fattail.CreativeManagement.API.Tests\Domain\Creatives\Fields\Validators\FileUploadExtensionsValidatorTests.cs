﻿using Bogus;
using Fattail.CreativeManagement.API.Domain.Common;
using Fattail.CreativeManagement.API.Domain.Common.Errors;
using Fattail.CreativeManagement.API.Domain.CreativeFields;
using Fattail.CreativeManagement.API.Domain.CreativeFiles;
using Fattail.CreativeManagement.API.Domain.Creatives;
using Fattail.CreativeManagement.API.Domain.Creatives.Fields;
using Fattail.CreativeManagement.API.Domain.Creatives.Fields.Validators;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates.Fields;
using FluentAssertions;
using FluentResults;
using FluentResults.Extensions.FluentAssertions;
using Moq;
using NUnit.Framework;

namespace Fattail.CreativeManagement.API.Tests.Domain.Creatives.Fields.Validators
{
    [TestFixture]
    public class FileUploadExtensionsValidatorTests
    {
        private static readonly List<string> _validationOptions = new() { ".TXT", ".JPG" };

        private CreativeFieldIdentifier _creativeFieldIdentifier;
        private readonly string _validationOptionsAsString = string.Join(", ", _validationOptions);

        private readonly IEnumerable<ValidationRule> _validationRule = new List<ValidationRule>
        {
            ValidationRule.Create(CreativeFieldValidationRuleType.FileUploadExtensions, _validationOptions, new CreativeFieldId(1234)).Value
        };

        protected FileUploadExtensionsRuleValidator _fileUploadExtensionsRuleValidator = null!;
        private Mock<ICreativeFilesUploadPolicy> _creativeFilesUploadPolicyMock = null!;
        private Mock<IIdManager> _idManagerMock = null!;

        [SetUp]
        public void SetUp ()
        {
            var creativeFieldId = new CreativeFieldId(123456);
            CreativeFieldType creativeFieldType = CreativeFieldType.MultiFileUpload;
            _creativeFieldIdentifier = CreativeFieldIdentifier.Create(creativeFieldId, creativeFieldType);

            _fileUploadExtensionsRuleValidator = new FileUploadExtensionsRuleValidator(_validationRule, _creativeFieldIdentifier);

            _creativeFilesUploadPolicyMock = new Mock<ICreativeFilesUploadPolicy>();
            _creativeFilesUploadPolicyMock.SetupGet(oap => oap.MaxSizeInMegabytesAllowed).Returns(1000);
            _creativeFilesUploadPolicyMock.SetupGet(oap => oap.AllowedExtensions).Returns(
                new HashSet<string>(StringComparer.FromComparison(StringComparison.OrdinalIgnoreCase))
                {
                    ".txt", ".pdf", ".jpg"
                });

            _idManagerMock = new Mock<IIdManager>();
            _idManagerMock.Setup(idManager => idManager.GetId()).Returns(new Faker().Random.Long(1));
        }

        [Test]
        public void File_upload_extensions_validation_verifier_verify_for_single_file_passes ()
        {
            IReadOnlyList<CreativeFile?> creativeFiles = new List<CreativeFile?> { GetCreativeFile(1234, "file1.txt") };
            Result result = _fileUploadExtensionsRuleValidator.IsValid(creativeFiles);

            result.Should().BeSuccess();
        }

        [Test]
        public void File_upload_extensions_validation_verifier_verify_for_single_file_fails ()
        {
            IReadOnlyList<CreativeFile?> creativeFiles = new List<CreativeFile?> { GetCreativeFile(1234, "file1.pdf") };
            Result result = _fileUploadExtensionsRuleValidator.IsValid(creativeFiles);

            result.Should().BeFailure().And
                .HaveReason(new FileUploadExtensionsValidationError(nameof(CreativeFieldValue), nameof(Creative), _creativeFieldIdentifier.Id.ToString(), _validationOptionsAsString));
        }

        [Test]
        public void File_upload_extensions_validation_verifier_verify_for_list_of_files_passes ()
        {
            var fileValues =
                new List<CreativeFile> { GetCreativeFile(1234, "file1.txt"), GetCreativeFile(5678, "file2.jpg") };
            Result result = _fileUploadExtensionsRuleValidator.IsValid(fileValues);

            result.Should().BeSuccess();
        }

        [Test]
        public void File_upload_extensions_validation_verifier_verify_for_list_of_files_fails_once ()
        {
            var fileValues =
                new List<CreativeFile> { GetCreativeFile(1234, "file1.txt"), GetCreativeFile(5678, "file2.pdf") };
            Result result = _fileUploadExtensionsRuleValidator.IsValid(fileValues);

            result.Should().BeFailure().And
                .HaveReason(new FileUploadExtensionsValidationError(nameof(CreativeFieldValue), nameof(Creative), _creativeFieldIdentifier.Id.ToString(), _validationOptionsAsString));
        }

        [Test]
        public void File_upload_extensions_validation_verifier_verify_for_list_of_invalid_files_fails_once ()
        {
            var fileValues =
                new List<CreativeFile> { GetCreativeFile(1234, "file1.pdf"), GetCreativeFile(5678, "file2.pdf") };
            Result result = _fileUploadExtensionsRuleValidator.IsValid(fileValues);

            result.Should().BeFailure().And
                .HaveReason(new FileUploadExtensionsValidationError(nameof(CreativeFieldValue), nameof(Creative), _creativeFieldIdentifier.Id.ToString(), _validationOptionsAsString));
            result.Errors.Should().ContainSingle();
        }

        private CreativeFile GetCreativeFile (long id, string fileName)
        {
            var creativeFileName = CreativeFileName.From(fileName);

            _idManagerMock.Setup(idManager => idManager.GetId()).Returns(id);

            return CreativeFile.PrepareToUpload(_creativeFilesUploadPolicyMock.Object,
                _idManagerMock.Object, creativeFileName, Stream.Null).Value;
        }
    }
}