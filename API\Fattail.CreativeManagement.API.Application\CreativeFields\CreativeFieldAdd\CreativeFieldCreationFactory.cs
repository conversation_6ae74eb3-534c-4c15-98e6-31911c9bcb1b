﻿using Fattail.CreativeManagement.API.Domain.Common;
using Fattail.CreativeManagement.API.Domain.Common.Errors;
using Fattail.CreativeManagement.API.Domain.CreativeFields;
using Fattail.CreativeManagement.API.Domain.CreativeFields.Specifications;
using Fattail.CreativeManagement.API.Domain.Repositories;
using SelectOptionDomain = Fattail.CreativeManagement.API.Domain.CreativeFields.SelectOption;
using FluentResults;

namespace Fattail.CreativeManagement.API.Application.CreativeFields.CreativeFieldAdd;

internal class CreativeFieldCreationFactory : ICreativeFieldCreationFactory
{
    public async Task<Result<CreativeField>> CreateField (IIdManager idManager,
        ICreativeFieldRepository creativeFieldRepository,
        CreativeFieldAddCommand requestModel)
    {
        if (requestModel.Type is null)
        {
            return Result.Fail(new InvalidCreativeFieldType("creative field type", nameof(CreateField)));
        }

        var creativeFieldId = new CreativeFieldId(idManager.GetId());

        var creativeFieldNameInUseSpecification = new CreativeFieldNameInUseSpecification(requestModel.Name, requestModel.Type);
        var creativeFieldUniqueNameRequirement = new CreativeFieldUniqueNameRequirement(await creativeFieldRepository.FindAsync(creativeFieldNameInUseSpecification) == null);

        return requestModel.Type switch
        {
            CreativeFieldType.SingleLineText or 
            CreativeFieldType.MultiLineText or 
            CreativeFieldType.MultiFileUpload or 
            CreativeFieldType.FileUpload or
            CreativeFieldType.SectionDivider => CreativeField.Create(creativeFieldId, requestModel.Name, (CreativeFieldType)requestModel.Type, creativeFieldUniqueNameRequirement, false, null),

            CreativeFieldType.MultiSelectOption => MultiSelectCreativeField.Create(creativeFieldId, requestModel.Name, (CreativeFieldType)requestModel.Type, requestModel.Options.Select(x => SelectOptionDomain.Create(x.Id, x.Description)).ToList().AsReadOnly(), creativeFieldUniqueNameRequirement, false, null),

            CreativeFieldType.SingleSelectOption => SingleSelectCreativeField.Create(creativeFieldId, requestModel.Name, (CreativeFieldType)requestModel.Type, requestModel.Options.Select(x => SelectOptionDomain.Create(x.Id, x.Description)).ToList().AsReadOnly(), creativeFieldUniqueNameRequirement, false, null),
            
            _ => throw new NotImplementedException()
        };
    }
}