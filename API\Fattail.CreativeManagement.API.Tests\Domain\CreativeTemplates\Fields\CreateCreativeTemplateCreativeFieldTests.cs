﻿using Fattail.CreativeManagement.API.Domain.CreativeFields;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates.Fields;
using FluentResults;
using NUnit.Framework;
using FluentAssertions;
using FluentResults.Extensions.FluentAssertions;
using Fattail.CreativeManagement.API.Domain.Common.Errors;

namespace Fattail.CreativeManagement.API.Tests.Domain.CreativeTemplates.Fields
{
    [TestFixture]
    public class CreateCreativeTemplateCreativeFieldTests
    {
        private static readonly CreativeFieldType[] _existingCreativeFields =
            ((CreativeFieldType[])Enum.GetValues(typeof(CreativeFieldType)))
            .ToArray();
        private readonly string _fieldName = "fieldName";
        private readonly long _fieldId = 1234;

        [Test]
        public void Creative_template_field_cant_be_created_if_creative_field_do_not_exist()
        {
            var creativeFieldId = new CreativeFieldId(_fieldId);

            Result<CreativeTemplateCreativeField> creativeTemplateFieldResult = CreativeTemplateCreativeField.Create(creativeFieldId,
                new HashSet<CreativeField>(), 1);

            creativeTemplateFieldResult.Should().BeFailure();
        }
        
        [Test]
        public void Creative_template_field_cant_be_created_with_empty_field_id_fails ()
        {
            Result<CreativeTemplateCreativeField> creativeTemplateFieldResult =
                CreativeTemplateCreativeField.Create(null,
                    new HashSet<CreativeField>(), 1);

            creativeTemplateFieldResult.Should().BeFailure().And
                .HaveReason(new RequiredValueMissingError("creative field id", nameof(CreativeTemplateCreativeField)));
        }

        [Test]
        [TestCaseSource(nameof(_existingCreativeFields))]
        public void Creative_template_field_can_be_created (CreativeFieldType creativeFieldType)
        {
            var creativeFieldId = new CreativeFieldId(_fieldId);

            CreativeField? creativeField = null;

            var creativeFieldUniqueNameRequirement = new CreativeFieldUniqueNameRequirement(true);

            switch (creativeFieldType)
            {
                case CreativeFieldType.MultiSelectOption:
                    creativeField = MultiSelectCreativeField.Create(creativeFieldId, _fieldName, creativeFieldType, Array.Empty<SelectOption>(), creativeFieldUniqueNameRequirement, false, null).Value;
                    break;
                case CreativeFieldType.SingleSelectOption:
                    creativeField = SingleSelectCreativeField.Create(creativeFieldId, _fieldName, creativeFieldType, Array.Empty<SelectOption>(), creativeFieldUniqueNameRequirement, false, null).Value;
                    break;
                default:
                    creativeField = CreativeField.Create(creativeFieldId, _fieldName, creativeFieldType, creativeFieldUniqueNameRequirement, false, null).Value;
                    break;
            }
            
            Result<CreativeTemplateCreativeField> creativeTemplateFieldResult = CreativeTemplateCreativeField.Create(creativeFieldId,
                new HashSet<CreativeField>([creativeField]), 1);

            creativeTemplateFieldResult.Should().BeSuccess();

            CreativeTemplateCreativeField creativeTemplateField = creativeTemplateFieldResult.Value;
            creativeTemplateField.Name.Should().BeEquivalentTo(_fieldName);
            creativeTemplateField.DisplayOrder.Should().Be(1);
            creativeTemplateField.ValidationRules.Should().BeEmpty();
        }
    }
}
