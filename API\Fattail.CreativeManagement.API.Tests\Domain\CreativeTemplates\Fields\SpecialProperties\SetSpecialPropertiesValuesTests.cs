﻿using Fattail.CreativeManagement.API.Domain.Common;
using Fattail.CreativeManagement.API.Domain.CreativeFields;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates.Errors;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates.Fields;
using Fattail.CreativeManagement.API.Domain.Repositories;
using FluentResults;
using FluentResults.Extensions.FluentAssertions;
using Moq;
using NUnit.Framework;

namespace Fattail.CreativeManagement.API.Tests.Domain.CreativeTemplates.Fields.SpecialProperties;

[TestFixture]
public class SetSpecialPropertiesValuesTests
{
    private readonly string _fieldName = "fieldName";
    private readonly long _fieldId = 1234;

    [Test]
    public async Task Special_properties_cant_be_set_for_unsupported_types ()
    {
        CreativeTemplateCreativeField creativeField = CreateCreativeTemplateField();

        Result setSpecialPropertiesValuesResult = creativeField.SetSpecialPropertiesValues(new Dictionary<SpecialPropertyKey, object?>());

        setSpecialPropertiesValuesResult.Should().BeFailure()
            .And.HaveReason(new CreativeTemplateFieldSpecialPropertiesNotSupportedError(creativeField.Id, nameof(CreativeTemplateCreativeField)));
    }

    private CreativeTemplateCreativeField CreateCreativeTemplateField ()
    {
        var creativeFieldId = new CreativeFieldId(_fieldId);
        var creativeFieldUniqueNameRequirement = new CreativeFieldUniqueNameRequirement(true);

        CreativeField creativeField = CreativeField.Create(creativeFieldId, _fieldName, It.IsAny<CreativeFieldType>(), creativeFieldUniqueNameRequirement, false, null).Value;
        
        Result<CreativeTemplateCreativeField> creativeTemplateFieldResult = CreativeTemplateCreativeField.Create(creativeFieldId, new HashSet<CreativeField>([creativeField]), 1);

        return creativeTemplateFieldResult.Value;
    }
}
