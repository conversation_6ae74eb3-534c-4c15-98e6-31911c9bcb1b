﻿using FluentResults;

namespace Fattail.CreativeManagement.API.Domain.CreativeFields
{
    public class SingleSelectCreativeField : CreativeField
    {
        private SingleSelectCreativeField (CreativeFieldId id, string name, CreativeFieldType type,
            IReadOnlyList<SelectOption> options, bool predefined, string? omsExternalIdentifier) : base(id, name, type, predefined, omsExternalIdentifier)
        {
            Options = options;
        }

        public IReadOnlyList<SelectOption> Options { get; }

        public static Result<CreativeField> Create (CreativeFieldId id, string? name,
            CreativeFieldType type, IReadOnlyList<SelectOption> options,
            CreativeFieldUniqueNameRequirement creativeFieldUniqueNameRequirement,
            bool predefined, string? omsExternalIdentifier)
        {
            Result result = CanCreateCreativeField(name, type, creativeFieldUniqueNameRequirement);

            if (result.IsFailed)
            {
                return result;
            }

            var creativeField =
                new SingleSelectCreativeField(id, name!, type, options, predefined, omsExternalIdentifier);

            return creativeField;
        }
    }
}