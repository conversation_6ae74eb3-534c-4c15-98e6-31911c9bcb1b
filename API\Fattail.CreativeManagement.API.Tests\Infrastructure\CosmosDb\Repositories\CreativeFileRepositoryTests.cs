using AutoMapper;
using Fattail.CreativeManagement.API.Application;
using Fattail.CreativeManagement.API.Application.CreativeFiles.CreativeFilesUpload;
using Fattail.CreativeManagement.API.Domain.CreativeFiles;
using Fattail.CreativeManagement.API.Domain.CreativeFiles.Types;
using Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Entities;
using Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Interfaces;
using Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Repositories;
using Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Settings;
using FluentAssertions;
using Microsoft.Azure.Cosmos;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;
using NUnit.Framework;
using System.Net;
using Entities = Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Entities;

namespace Fattail.CreativeManagement.API.Tests.Infrastructure.CosmosDb.Repositories;

[TestFixture]
public class CreativeFileRepositoryTests
{
    private Mock<ICosmosDbContainerFactory> _cosmosDbContainerFactoryMock = null!;
    private Mock<IMapper> _mapperMock = null!;
    private Mock<IOrganizationContext> _organizationContextMock = null!;
    private Mock<IOptions<ParallelExecutionSettings>> _parallelExecutionSettingsMock = null!;
    private Mock<ICreativeFileStorageManager> _creativeFileStorageManagerMock = null!;
    private Mock<ILogger<CreativeFileRepository>> _loggerMock = null!;
    private Mock<Container> _containerMock = null!;
    private CreativeFileRepository _repository = null!;

    [SetUp]
    public void SetUp ()
    {
        _cosmosDbContainerFactoryMock = new Mock<ICosmosDbContainerFactory>();
        _mapperMock = new Mock<IMapper>();
        _organizationContextMock = new Mock<IOrganizationContext>();
        _parallelExecutionSettingsMock = new Mock<IOptions<ParallelExecutionSettings>>();
        _creativeFileStorageManagerMock = new Mock<ICreativeFileStorageManager>();
        _loggerMock = new Mock<ILogger<CreativeFileRepository>>();
        _containerMock = new Mock<Container>();

        _organizationContextMock.Setup(x => x.OrganizationId).Returns(123);
        _parallelExecutionSettingsMock.Setup(x => x.Value).Returns(new ParallelExecutionSettings());
        _cosmosDbContainerFactoryMock.Setup(x => x.GetContainer(It.IsAny<string>())).Returns(_containerMock.Object);

        _repository = new CreativeFileRepository(
            _cosmosDbContainerFactoryMock.Object,
            _mapperMock.Object,
            _organizationContextMock.Object,
            _parallelExecutionSettingsMock.Object,
            _creativeFileStorageManagerMock.Object,
            _loggerMock.Object);
    }

    [Test]
    public async Task FindByIdAsync_SelfHealsSize_WhenSizeIsZero ()
    {
        // Arrange
        var creativeFileId = new CreativeFileId(123);
        var cosmosEntity = new Entities.CreativeFile
        {
            Id = "123",
            Name = "test.jpg",
            BlobName = "test-blob",
            Extension = ".jpg",
            Type = CreativeFileTypeEnum.Image,
            Size = 0, // Missing size
            Metadata = new Dictionary<string, string> { { "Width", "100" }, { "Height", "200" } }
        };

        var itemResponse = new Mock<ItemResponse<Entities.CreativeFile>>();
        itemResponse.Setup(x => x.Resource).Returns(cosmosEntity);

        _containerMock.Setup(x => x.ReadItemAsync<Entities.CreativeFile>(
                It.IsAny<string>(), It.IsAny<PartitionKey>(), null, default))
            .ReturnsAsync(itemResponse.Object);

        _creativeFileStorageManagerMock.Setup(x => x.GetFileSizeInBytesAsync("test-blob"))
            .ReturnsAsync(1024);

        var domainEntity = new Mock<Domain.CreativeFiles.CreativeFile>();
        _mapperMock.Setup(x => x.Map<Domain.CreativeFiles.CreativeFile>(It.IsAny<Entities.CreativeFile>()))
            .Returns(domainEntity.Object);

        // Act
        var result = await _repository.FindByIdAsync(creativeFileId);

        // Assert
        result.Should().NotBeNull();
        cosmosEntity.Size.Should().Be(1024);
        _creativeFileStorageManagerMock.Verify(x => x.GetFileSizeInBytesAsync("test-blob"), Times.Once);
    }

    [Test]
    public async Task FindByIdAsync_SelfHealsType_WhenTypeIsOther ()
    {
        // Arrange
        var creativeFileId = new CreativeFileId(123);
        var cosmosEntity = new Entities.CreativeFile
        {
            Id = "123",
            Name = "test.jpg",
            BlobName = "test-blob",
            Extension = ".jpg",
            Type = CreativeFileTypeEnum.Other, // Wrong type
            Size = 1024,
            Metadata = new Dictionary<string, string>()
        };

        var itemResponse = new Mock<ItemResponse<Entities.CreativeFile>>();
        itemResponse.Setup(x => x.Resource).Returns(cosmosEntity);

        _containerMock.Setup(x => x.ReadItemAsync<Entities.CreativeFile>(
                It.IsAny<string>(), It.IsAny<PartitionKey>(), null, default))
            .ReturnsAsync(itemResponse.Object);

        var domainEntity = new Mock<Domain.CreativeFiles.CreativeFile>();
        _mapperMock.Setup(x => x.Map<Domain.CreativeFiles.CreativeFile>(It.IsAny<Entities.CreativeFile>()))
            .Returns(domainEntity.Object);

        // Act
        var result = await _repository.FindByIdAsync(creativeFileId);

        // Assert
        result.Should().NotBeNull();
        cosmosEntity.Type.Should().Be(CreativeFileTypeEnum.Image);
    }

    [Test]
    public async Task FindByIdAsync_SelfHealsMetadata_WhenMetadataIsEmpty ()
    {
        // Arrange
        var creativeFileId = new CreativeFileId(123);
        var cosmosEntity = new Entities.CreativeFile
        {
            Id = "123",
            Name = "test.jpg",
            BlobName = "test-blob",
            Extension = ".jpg",
            Type = CreativeFileTypeEnum.Image,
            Size = 1024,
            Metadata = new Dictionary<string, string>() // Empty metadata
        };

        var itemResponse = new Mock<ItemResponse<Entities.CreativeFile>>();
        itemResponse.Setup(x => x.Resource).Returns(cosmosEntity);

        _containerMock.Setup(x => x.ReadItemAsync<Entities.CreativeFile>(
                It.IsAny<string>(), It.IsAny<PartitionKey>(), null, default))
            .ReturnsAsync(itemResponse.Object);

        var fileStream = new MemoryStream(new byte[] { 0x89, 0x50, 0x4E, 0x47 }); // PNG header
        _creativeFileStorageManagerMock.Setup(x => x.GetFileStreamAsync("test-blob"))
            .ReturnsAsync(fileStream);

        var domainEntity = new Mock<Domain.CreativeFiles.CreativeFile>();
        _mapperMock.Setup(x => x.Map<Domain.CreativeFiles.CreativeFile>(It.IsAny<Entities.CreativeFile>()))
            .Returns(domainEntity.Object);

        // Act
        var result = await _repository.FindByIdAsync(creativeFileId);

        // Assert
        result.Should().NotBeNull();
        _creativeFileStorageManagerMock.Verify(x => x.GetFileStreamAsync("test-blob"), Times.Once);
    }

    [Test]
    public async Task FindByIdAsync_DoesNotSelfHeal_WhenAllPropertiesAreValid ()
    {
        // Arrange
        var creativeFileId = new CreativeFileId(123);
        var cosmosEntity = new Entities.CreativeFile
        {
            Id = "123",
            Name = "test.jpg",
            BlobName = "test-blob",
            Extension = ".jpg",
            Type = CreativeFileTypeEnum.Image,
            Size = 1024,
            Metadata = new Dictionary<string, string> { { "Width", "100" }, { "Height", "200" } }
        };

        var itemResponse = new Mock<ItemResponse<Entities.CreativeFile>>();
        itemResponse.Setup(x => x.Resource).Returns(cosmosEntity);

        _containerMock.Setup(x => x.ReadItemAsync<Entities.CreativeFile>(
                It.IsAny<string>(), It.IsAny<PartitionKey>(), null, default))
            .ReturnsAsync(itemResponse.Object);

        var domainEntity = new Mock<Domain.CreativeFiles.CreativeFile>();
        _mapperMock.Setup(x => x.Map<Domain.CreativeFiles.CreativeFile>(It.IsAny<Entities.CreativeFile>()))
            .Returns(domainEntity.Object);

        // Act
        var result = await _repository.FindByIdAsync(creativeFileId);

        // Assert
        result.Should().NotBeNull();
        _creativeFileStorageManagerMock.Verify(x => x.GetFileSizeInBytesAsync(It.IsAny<string>()), Times.Never);
        _creativeFileStorageManagerMock.Verify(x => x.GetFileStreamAsync(It.IsAny<string>()), Times.Never);
    }

    [Test]
    public async Task FindByIdAsync_ReturnsNull_WhenEntityNotFound ()
    {
        // Arrange
        var creativeFileId = new CreativeFileId(123);

        _containerMock.Setup(x => x.ReadItemAsync<Entities.CreativeFile>(
                It.IsAny<string>(), It.IsAny<PartitionKey>(), null, default))
            .ThrowsAsync(new CosmosException("Not found", HttpStatusCode.NotFound, 0, "", 0));

        // Act
        var result = await _repository.FindByIdAsync(creativeFileId);

        // Assert
        result.Should().BeNull();
    }
}
