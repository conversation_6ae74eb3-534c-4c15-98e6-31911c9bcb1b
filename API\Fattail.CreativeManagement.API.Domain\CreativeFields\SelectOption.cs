﻿using Dawn;
using Fattail.CreativeManagement.API.Domain.Common;

namespace Fattail.CreativeManagement.API.Domain.CreativeFields
{
    public sealed class SelectOption : ValueObject
    {
        private SelectOption (long id, string description)
        {
            Id = id;
            Description = description;
        }

        public long Id { get; }
        public string Description { get; }

        protected override IEnumerable<object?> GetEqualityComponents ()
        {
            yield return Id;
        }

        public static SelectOption Create (long id, string description)
        {
            Guard.Argument(id, nameof(id)).NotEqual(0);

            return new SelectOption(id, description);
        }
    }
}
