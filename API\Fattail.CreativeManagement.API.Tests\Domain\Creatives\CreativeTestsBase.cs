﻿using Bogus;
using Fattail.CreativeManagement.API.Domain.Common;
using Fattail.CreativeManagement.API.Domain.CreativeFields;
using Fattail.CreativeManagement.API.Domain.Creatives;
using Fattail.CreativeManagement.API.Domain.Creatives.Factory;
using Fattail.CreativeManagement.API.Domain.Creatives.Fields.Sanitizers;
using Fattail.CreativeManagement.API.Domain.Creatives.Fields.Sanitizers.StrategyProvider;
using Fattail.CreativeManagement.API.Domain.Creatives.Fields.Validators;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates.Factory;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates.Requirements;
using FluentResults;
using Moq;
using NUnit.Framework;

namespace Fattail.CreativeManagement.API.Tests.Domain.Creatives;

public abstract class CreativeTestsBase
{
    protected Mock<ISanitizerStrategyProvider> _sanitizerStrategyProviderMock = null!;
    protected Mock<ISanitizer> _sanitizerMock = null!;
    protected CreativeFieldUniqueNameRequirement _creativeFieldUniqueNameRequirement = null!;
    private Mock<ICreativeFilesManager> _creativeFilesManagerMock = null!;
    protected ICreativeFactory _creativeFactory = null!;
    private const int GeneratedCreativeId = 12345;
    private readonly string _fieldName = "fieldName";

    [SetUp]
    public void SetUp ()
    {
        _creativeFilesManagerMock = new Mock<ICreativeFilesManager>();

        _sanitizerStrategyProviderMock = new Mock<ISanitizerStrategyProvider>();
        _sanitizerMock = new Mock<ISanitizer>();

        _sanitizerStrategyProviderMock
            .Setup(sanitizerStrategyProvider => sanitizerStrategyProvider.GetFrom(CreativeFieldType.MultiFileUpload))
            .Returns(_sanitizerMock.Object);

        _creativeFieldUniqueNameRequirement = new CreativeFieldUniqueNameRequirement(true);

        _creativeFactory = new CreativeFactory(_sanitizerStrategyProviderMock.Object);
    }

    protected async
        Task<(DateTime lastUpdatedOn, CreativeTemplate creativeTemplate, string creativeName, long adBookClientId, long campaignId, string
            updater, Result<Creative> creativeResult)> CreateCreative ()
    {
        var faker = new Faker();

        DateTime lastUpdatedOn = DateTime.UtcNow;

        long creativeFieldId1 = faker.Random.Long(11);
        long creativeFieldId2 = faker.Random.Long(12);
        long creativeFieldId3 = faker.Random.Long(13);

        var createCreativeTemplateRequest = new CreativeTemplateCreateRequest(
            new CreativeTemplateId(GeneratedCreativeId),
            CreativeTemplateName.Create("creative template name", new CreativeTemplateUniqueNameRequirement(true)).Value,
            CreativeType.Undefined,
            [
                new(new(creativeFieldId1), 1),
                new(new(creativeFieldId2), 2),
                new(new(creativeFieldId3), 3)
            ],
            new HashSet<CreativeField>([
                CreativeField.Create(new(creativeFieldId1), _fieldName, CreativeFieldType.MultiFileUpload, _creativeFieldUniqueNameRequirement, false, null).Value,
                CreativeField.Create(new(creativeFieldId2), _fieldName, CreativeFieldType.MultiFileUpload, _creativeFieldUniqueNameRequirement, false, null).Value,
                CreativeField.Create(new(creativeFieldId3), _fieldName, CreativeFieldType.SectionDivider, _creativeFieldUniqueNameRequirement, false, null).Value
            ]),
            false
        );

        Result<CreativeTemplate> creativeTemplateResult = CreativeTemplateFactory.Create(createCreativeTemplateRequest);

        string creativeName = faker.Random.String();
        long adBookClientId = faker.Random.Long(1);
        long campaignId = faker.Random.Long(1);
        var lineItemIds = Enumerable.Range(1, 7)
            .Select(_ => faker.Random.Long())
            .ToHashSet();
        string updater = faker.Random.String();

        Result<Creative> creativeResult = await _creativeFactory.Create(new CreateCreativeRequest(GeneratedCreativeId, creativeName,
            creativeTemplateResult.Value, adBookClientId, null, campaignId, lineItemIds, updater,
            new Dictionary<CreativeFieldId, object?>(), lastUpdatedOn));

        return (lastUpdatedOn, creativeTemplateResult.Value, creativeName, adBookClientId, campaignId, updater, creativeResult);
    }

    protected async
        Task<(DateTime lastUpdatedOn, CreativeTemplate creativeTemplate, string creativeName, long adBookClientId, long campaignId, string
            updater, Result<Creative> creativeResult)> CreateCreativeWithInitialFieldValues (
            IReadOnlyDictionary<CreativeFieldId, object?> initialFieldValues)
    {
        var faker = new Faker();

        DateTime lastUpdatedOn = DateTime.UtcNow;

        var createCreativeTemplateRequest = new CreativeTemplateCreateRequest(
            new CreativeTemplateId(GeneratedCreativeId),
            CreativeTemplateName.Create("creative template name", new CreativeTemplateUniqueNameRequirement(true)).Value,
            CreativeType.Undefined,
            [
                new(new(11), 1),
                new(new(12), 2)
            ],
            new HashSet<CreativeField>([
                CreativeField.Create(new(11), _fieldName, CreativeFieldType.SingleLineText, _creativeFieldUniqueNameRequirement, false, null).Value,
                CreativeField.Create(new(12), _fieldName, CreativeFieldType.SingleLineText, _creativeFieldUniqueNameRequirement, false, null).Value
            ]),
            false
        );

        Result<CreativeTemplate> creativeTemplateResult = CreativeTemplateFactory.Create(createCreativeTemplateRequest);

        string creativeName = faker.Random.String();
        long adBookClientId = faker.Random.Long(1);
        long campaignId = faker.Random.Long(1);
        var lineItemIds = Enumerable.Range(1, 7)
            .Select(_ => faker.Random.Long(1, 10000))
            .ToHashSet();
        string updater = faker.Random.String();

        Result<Creative> creativeResult = await _creativeFactory.Create(new CreateCreativeRequest(GeneratedCreativeId, creativeName,
            creativeTemplateResult.Value, adBookClientId, null, campaignId, lineItemIds, updater, initialFieldValues, lastUpdatedOn));

        return (lastUpdatedOn, creativeTemplateResult.Value, creativeName, adBookClientId, campaignId, updater, creativeResult);
    }
}