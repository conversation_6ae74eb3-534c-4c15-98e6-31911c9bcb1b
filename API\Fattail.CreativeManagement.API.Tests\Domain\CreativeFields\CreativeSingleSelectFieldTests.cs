﻿using Fattail.CreativeManagement.API.Domain.Common;
using Fattail.CreativeManagement.API.Domain.Common.Errors;
using Fattail.CreativeManagement.API.Domain.CreativeFields;
using Fattail.CreativeManagement.API.Domain.CreativeFields.Errors;
using FluentAssertions;
using FluentResults;
using FluentResults.Extensions.FluentAssertions;
using Moq;
using NUnit.Framework;

namespace Fattail.CreativeManagement.API.Tests.Domain.CreativeFields;

[TestFixture]
public class CreativeSingleSelectFieldTests
{
    [TestCase("")]
    [TestCase(" ")]
    [TestCase(null)]
    public void Creative_single_select_field_cant_be_created_without_name (string? name)
    {
        var creativeFieldUniqueNameRequirement = new CreativeFieldUniqueNameRequirement(true);

        Result<CreativeField> creativeFieldResult = SingleSelectCreativeField.Create(new (1234),
            name,
            CreativeFieldType.SingleSelectOption,
            new List<SelectOption>(),
            creativeFieldUniqueNameRequirement, false, null);

        creativeFieldResult.IsFailed.Should().BeTrue();
        creativeFieldResult.Should().BeFailure()
            .And.HaveReason(new RequiredValueMissingError("creative field name", nameof(CreativeField)));
    }
    
    [Test]
    public void Creative_singleselect_field_cant_be_created_with_duplicate_name_and_type ()
    {
        string creativeFieldName = "Creative field name";
        CreativeFieldType creativeFieldType = CreativeFieldType.SingleSelectOption;
        var creativeFieldUniqueNameRequirement = new CreativeFieldUniqueNameRequirement(false);
        
        Result<CreativeField> creativeFieldResult = CreativeField.Create(new (1234),
            creativeFieldName,
            creativeFieldType,
            creativeFieldUniqueNameRequirement, false, null);
        
        creativeFieldResult.Should().BeFailure()
            .And.HaveReason(new CreativeFieldNameInUseError(creativeFieldName, creativeFieldType.ToString(), nameof(CreativeField)));
    }
    
    [Test]
    public void Creative_single_select_field_can_be_created ()
    {
        string creativeFieldName = "Creative field name";
        var creativeFieldUniqueNameRequirement = new CreativeFieldUniqueNameRequirement(true);
        CreativeFieldType creativeFieldType = CreativeFieldType.SingleSelectOption;

        Result<CreativeField> creativeTemplateResult = CreativeField.Create(new (1234),
            creativeFieldName,
            creativeFieldType,
            creativeFieldUniqueNameRequirement, false, null);

        creativeTemplateResult.Should().BeSuccess();
        creativeTemplateResult.Value.Should().NotBeNull();
        creativeTemplateResult.Value.Name.Should().BeEquivalentTo(creativeFieldName);
        creativeTemplateResult.Value.Type.Should().Be(creativeFieldType);
    }
}