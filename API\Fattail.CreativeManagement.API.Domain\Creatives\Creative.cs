﻿using Dawn;
using Fattail.CreativeManagement.API.Domain.Common;
using Fattail.CreativeManagement.API.Domain.Common.Errors;
using Fattail.CreativeManagement.API.Domain.Creatives.Fields;
using Fattail.CreativeManagement.API.Domain.Creatives.Fields.Sanitizers.StrategyProvider;
using Fattail.CreativeManagement.API.Domain.Creatives.Fields.Validators;
using Fattail.CreativeManagement.API.Domain.Creatives.Statuses;
using Fattail.CreativeManagement.API.Domain.Creatives.Statuses.Errors;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates.Fields;
using FluentResults;

namespace Fattail.CreativeManagement.API.Domain.Creatives;

public sealed class Creative : Entity<long>
{
    private readonly Dictionary<CreativeFieldIdentifier, CreativeFieldValue> _fields;
    private HashSet<long> _lineItemIds;

    internal Creative (
        long id,
        string name,
        long? adBookClientId,
        long? adBookAdId,
        long campaignId,
        HashSet<long> lineItemIds,
        CreativeTemplateId creativeTemplateId,
        Dictionary<CreativeFieldIdentifier, CreativeFieldValue> fields,
        CreativeStatus? status,
        string lastUpdatedBy,
        DateTime lastUpdatedOn,
        ApprovalInformation? lastApproval) : base(id)
    {
        Name = name;
        AdBookClientId = adBookClientId;
        AdBookAdId = adBookAdId;
        CampaignId = campaignId;
        _lineItemIds = lineItemIds;
        CreativeTemplateId = creativeTemplateId;
        Status = status ?? CreativeStatus.PendingApproval;
        LastUpdatedBy = lastUpdatedBy;
        LastUpdatedOn = lastUpdatedOn;
        LastApproval = lastApproval;
        _fields = fields;
    }

    public string Name { get; private set; }

    public long? AdBookAdId { get; }

    public long? AdBookClientId { get; }

    public long CampaignId { get; }

    public IReadOnlyList<long> LineItemIds => _lineItemIds.ToList().AsReadOnly();

    public CreativeTemplateId CreativeTemplateId { get; }

    public IReadOnlyList<CreativeFieldValue> Fields => _fields.Values.ToList().AsReadOnly();

    public CreativeStatus Status { get; private set; }

    public string LastUpdatedBy { get; private set; }

    public DateTime LastUpdatedOn { get; private set; }

    public ApprovalInformation? LastApproval { get; private set; }

    public async Task<Result> AssignValueToField (CreativeFieldIdentifier creativeFieldIdentifier, object? value, UpdateInformation updateInformation,
        List<ValidationRule> validationRules, ISanitizerStrategyProvider? sanitizerStrategyProvider = null)
    {
        CreativeFieldValue creativeFieldValue = _fields[creativeFieldIdentifier];
        Result<CreativeFieldValue> newCreativeFieldValueResult = await creativeFieldValue.GenerateNewValue(value, validationRules, sanitizerStrategyProvider);

        if (newCreativeFieldValueResult.IsFailed)
        {
            return newCreativeFieldValueResult.ToResult();
        }

        _fields[creativeFieldIdentifier] = newCreativeFieldValueResult.Value;

        ResetStatus();

        UpdateLastUpdate(updateInformation);

        return Result.Ok();
    }

    public Result EditName (string? name, UpdateInformation updateInformation)
    {
        var result = new Result();

        if (string.IsNullOrWhiteSpace(name))
        {
            result.WithError(new RequiredValueMissingError("creative name", nameof(Creative)));
        }

        if (result.IsFailed)
        {
            return result;
        }

        Name = name!;

        ResetStatus();

        UpdateLastUpdate(updateInformation);

        return Result.Ok();
    }

    private void ResetStatus ()
    {
        if (Status.CanTransitionTo(CreativeStatus.PendingApproval))
        {
            Status = CreativeStatus.PendingApproval;
        }
    }

    public Result<LineItemSetResult> SetLineItemAssignments (HashSet<long>? lineItemIds, UpdateInformation updateInformation)
    {
        var result = new Result();

        if (lineItemIds is null)
        {
            result.WithError(new RequiredValueMissingError(nameof(lineItemIds), nameof(Creative)));
        }

        if (result.IsFailed)
        {
            return result;
        }

        var unassignedLineItems = LineItemIds.Except(lineItemIds!).ToList();
        var assignedLineItems = lineItemIds!.Except(LineItemIds).ToList();

        foreach (long assignedLineItem in assignedLineItems)
        {
            AssignLineItem(assignedLineItem);
        }

        foreach (long unassignedLineItem in unassignedLineItems)
        {
            UnassignLineItem(unassignedLineItem);
        }

        UpdateLastUpdate(updateInformation);

        return new LineItemSetResult(assignedLineItems, unassignedLineItems);
    }

    private void AssignLineItem (long lineItemId)
    {
        _lineItemIds.Add(lineItemId);
    }

    private void UnassignLineItem (long lineItemId)
    {
        _lineItemIds.Remove(lineItemId);
    }

    public Result Approve (ApprovalInformation approvalInformation)
    {
        if (!Status.CanTransitionTo(CreativeStatus.Approved))
        {
            return Result.Fail(new CreativeInvalidStatusTransitionError(Status, CreativeStatus.Approved));
        }

        Status = CreativeStatus.Approved;
        LastApproval = approvalInformation;

        return Result.Ok();
    }

    private void UpdateLastUpdate (UpdateInformation updateInformation)
    {
        Guard.Argument(updateInformation).NotNull("Update information cannot be null.");

        LastUpdatedBy = updateInformation.UpdaterName;
        LastUpdatedOn = updateInformation.UpdateDateTime;
    }

    [Obsolete($"Use {nameof(SetLineItemAssignments)} instead.")]
    public void UpdateLineItemIds (HashSet<long>? lineItemIds)
    {
        _lineItemIds = lineItemIds;
    }

    public async Task<CreativeFieldValue> GetCreativeFieldValue (CreativeFieldIdentifier creativeFieldIdentifier)
    {
        return _fields[creativeFieldIdentifier];
    }
}