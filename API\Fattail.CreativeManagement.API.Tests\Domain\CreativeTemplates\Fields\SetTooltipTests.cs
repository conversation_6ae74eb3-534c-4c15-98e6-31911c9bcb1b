﻿using Fattail.CreativeManagement.API.Domain.CreativeFields;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates.Fields;
using FluentAssertions;
using FluentResults;
using Moq;
using NUnit.Framework;

namespace Fattail.CreativeManagement.API.Tests.Domain.CreativeTemplates.Fields;

[TestFixture]
public class SetTooltipTests
{
    private readonly string _fieldName = "fieldName";
    private readonly long _fieldId = 1234;

    [TestCase("")]
    [TestCase(null)]
    public void Set_empty_tooltip_is_allowed (string? tooltip)
    {
        CreativeTemplateCreativeField creativeTemplateField = CreateCreativeTemplateField();

        creativeTemplateField.SetTooltip(tooltip);

        creativeTemplateField.Tooltip.Should().BeNullOrEmpty();
    }

    [Test]
    public void Set_tooltip_is_allowed ()
    {
        CreativeTemplateCreativeField creativeTemplateField = CreateCreativeTemplateField();
        string tooltip = "tooltip";

        creativeTemplateField.SetTooltip(tooltip);

        creativeTemplateField.Tooltip.Should().Be(tooltip);
    }

    private CreativeTemplateCreativeField CreateCreativeTemplateField ()
    {
        var creativeFieldId = new CreativeFieldId(_fieldId);
        var creativeFieldUniqueNameRequirement = new CreativeFieldUniqueNameRequirement(true);

        CreativeField creativeField = CreativeField.Create(creativeFieldId, _fieldName, It.IsAny<CreativeFieldType>(), creativeFieldUniqueNameRequirement, false, null).Value;

        Result<CreativeTemplateCreativeField> creativeTemplateFieldResult = CreativeTemplateCreativeField.Create(creativeFieldId,
            new HashSet<CreativeField>([creativeField]), 1);

        return creativeTemplateFieldResult.Value;
    }
}