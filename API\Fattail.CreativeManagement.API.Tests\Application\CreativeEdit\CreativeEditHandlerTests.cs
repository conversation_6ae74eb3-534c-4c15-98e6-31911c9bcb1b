﻿using AutoBogus;
using Bogus;
using Fattail.CreativeManagement.API.Application.Creatives;
using Fattail.CreativeManagement.API.Application.Creatives.CreativeEdit;
using Fattail.CreativeManagement.API.Domain.Common;
using Fattail.CreativeManagement.API.Domain.Common.Errors;
using Fattail.CreativeManagement.API.Domain.CreativeFields;
using Fattail.CreativeManagement.API.Domain.Creatives;
using Fattail.CreativeManagement.API.Domain.Creatives.Factory;
using Fattail.CreativeManagement.API.Domain.Creatives.Fields.Sanitizers;
using Fattail.CreativeManagement.API.Domain.Creatives.Fields.Sanitizers.StrategyProvider;
using Fattail.CreativeManagement.API.Domain.Creatives.Fields.Validators;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates.Factory;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates.Requirements;
using Fattail.CreativeManagement.API.Domain.Repositories;
using FluentResults;
using FluentResults.Extensions.FluentAssertions;
using Moq;
using NUnit.Framework;
using CreativeFields = Fattail.CreativeManagement.API.Domain.Creatives.Fields;

namespace Fattail.CreativeManagement.API.Tests.Application.CreativeEdit;

[TestFixture]
public class CreativeEditHandlerTests
{
    [SetUp]
    public void SetUp ()
    {
        _idManagerMock = new Mock<IIdManager>();
        _idManagerMock.SetupSequence(idManager => idManager.GetId()).Returns(123).Returns(456);

        _dateTimeProviderMock = new Mock<IDateTimeProvider>();
        _creativeRepositoryMock = new Mock<ICreativeRepository>();
        _creativeTemplateRepositoryMock = new Mock<ICreativeTemplateRepository>();
        _sanitizerStrategyProviderMock = new Mock<ISanitizerStrategyProvider>();

        _sanitizerStrategyProviderMock = new Mock<ISanitizerStrategyProvider>();
        _sanitizerMock = new Mock<ISanitizer>();

        _sanitizerStrategyProviderMock
            .Setup(sanitizerStrategyProvider =>
                sanitizerStrategyProvider.GetFrom(CreativeFieldType.MultiFileUpload))
            .Returns(_sanitizerMock.Object);

        _creativeFilesManagerMock = new Mock<ICreativeFilesManager>();

        _creativeFactory = new CreativeFactory(_sanitizerStrategyProviderMock.Object);

        _creativeEditHandler = new CreativeEditHandler(
            _creativeRepositoryMock.Object,
            _creativeTemplateRepositoryMock.Object,
            _dateTimeProviderMock.Object,
            _sanitizerStrategyProviderMock.Object);
    }

    private CreativeEditHandler _creativeEditHandler = null!;
    private Mock<IDateTimeProvider> _dateTimeProviderMock = null!;
    private Mock<ICreativeRepository> _creativeRepositoryMock = null!;
    private Mock<ICreativeTemplateRepository> _creativeTemplateRepositoryMock = null!;
    private Mock<ISanitizerStrategyProvider> _sanitizerStrategyProviderMock = null!;
    private Mock<ISanitizer> _sanitizerMock = null!;
    private Mock<IIdManager> _idManagerMock = null!;
    private Mock<ICreativeFilesManager> _creativeFilesManagerMock = null!;
    private ICreativeFactory _creativeFactory = null!;
    private readonly string _fieldName = "fieldName";

    [Test]
    public async Task Valid_command_updates_creative ()
    {
        Creative creative = CreateCreative();
        CreativeEditCommand command = new AutoFaker<CreativeEditCommand>()
            .RuleFor(creativeEditCommand => creativeEditCommand.Id, faker => faker.Random.Long(1))
            .RuleFor(creativeEditCommand => creativeEditCommand.LineItemIds, faker => Enumerable.Range(1, 5)
                .Select(_ => faker.Random.Long())
                .ToHashSet())
            .RuleFor(creativeEditCommand => creativeEditCommand.Fields, faker => creative.Fields
                .Select<CreativeFields.CreativeFieldValue, CreativeFieldDto>(creativeField =>
                    creativeField.CreativeFieldIdentifier.Type switch
                    {
                        CreativeFieldType.MultiFileUpload => new MultiFileUploadFieldValue(
                            creativeField.CreativeFieldIdentifier.Id,
                            faker.Make(3, () => faker.Random.Long(1)).AsReadOnly()),
                        CreativeFieldType.SingleLineText => new SingleLineTextFieldValue(
                            creativeField.CreativeFieldIdentifier.Id, faker.Random.String()),
                        _ => throw new ArgumentOutOfRangeException()
                    }).ToList())
            .Generate();

        _creativeRepositoryMock.Setup(x => x.FindByIdAsync(command.Id)).ReturnsAsync(creative);

        Result<CreativeResult> result = await _creativeEditHandler.Handle(command, CancellationToken.None);

        result.Should().BeSuccess();
        _creativeRepositoryMock.Verify(x => x.UpdateAsync<CreativeResult>(creative), Times.Once);
    }

    [Test]
    public async Task Invalid_command_doesnt_update_creative ()
    {
        Creative creative = CreateCreative();
        CreativeEditCommand command = new AutoFaker<CreativeEditCommand>()
            .RuleFor(creativeEditCommand => creativeEditCommand.Name, () => string.Empty)
            .RuleFor(creativeEditCommand => creativeEditCommand.LineItemIds, faker => Enumerable.Range(1, 5)
                .Select(_ => faker.Random.Long())
                .ToHashSet())
            .RuleFor(creativeEditCommand => creativeEditCommand.Id, faker => faker.Random.Long(1))
            .Generate();
        _creativeRepositoryMock.Setup(x => x.FindByIdAsync(command.Id)).ReturnsAsync(creative);

        Result<CreativeResult> result = await _creativeEditHandler.Handle(command, CancellationToken.None);

        result.Should().BeFailure();
        _creativeRepositoryMock.Verify(x => x.UpdateAsync<CreativeResult>(creative), Times.Never);
    }

    [Test]
    public async Task Editing_unexisting_creative_fails_with_not_found_error ()
    {
        CreativeEditCommand command = new AutoFaker<CreativeEditCommand>().Generate();
        _creativeRepositoryMock.Setup(x => x.FindByIdAsync(command.Id)).ReturnsAsync((Creative?)null);

        Result<CreativeResult> result = await _creativeEditHandler.Handle(command, CancellationToken.None);

        result.Should().BeFailure()
            .And.HaveReason(new EntityNotFoundError(command.Id.ToString(), "Creative"));
        _creativeRepositoryMock.Verify(x => x.UpdateAsync<CreativeResult>(It.IsAny<Creative>()), Times.Never);
    }

    private Creative CreateCreative ()
    {
        var faker = new Faker();

        DateTime lastUpdatedOn = DateTime.UtcNow;
        _dateTimeProviderMock.SetupGet(dateTimeProvider => dateTimeProvider.CurrentTime).Returns(lastUpdatedOn);

        var uniqueNameRequirement = new CreativeFieldUniqueNameRequirement(true);

        var createCreativeTemplateRequest = new CreativeTemplateCreateRequest(
            new CreativeTemplateId(123),
            CreativeTemplateName.Create("creative template name", new CreativeTemplateUniqueNameRequirement(true)).Value,
            CreativeType.Undefined,
            [new(new(123), 1), new(new(456), 2)],
            new HashSet<CreativeField>([
                CreativeField.Create(new CreativeFieldId(123), _fieldName, CreativeFieldType.SingleLineText, uniqueNameRequirement, false, null).Value,
                CreativeField.Create(new CreativeFieldId(456), _fieldName, CreativeFieldType.MultiFileUpload, uniqueNameRequirement, false, null).Value
            ]),
            false
        );

        CreativeTemplate creativeTemplate = CreativeTemplateFactory.Create(createCreativeTemplateRequest).Value;

        string creativeName = faker.Random.String();
        long adBookClientId = faker.Random.Long(1);
        long campaignId = faker.Random.Long(1);
        var lineItems = new List<long> { 123, 456, 3 };
        string updater = faker.Random.String();

        Result<Creative> creativeResult = _creativeFactory.Create(new CreateCreativeRequest(
            _idManagerMock.Object.GetId(),
            creativeName,
            creativeTemplate,
            adBookClientId,
            null,
            campaignId,
            lineItems.ToHashSet(),
            updater,
            new Dictionary<CreativeFieldId, object?>(),
            _dateTimeProviderMock.Object.CurrentTime
        )).Result;

        return creativeResult.Value;
    }
}