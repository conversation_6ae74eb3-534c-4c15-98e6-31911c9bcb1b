﻿using Fattail.CreativeManagement.API.Domain.CreativeFields;
using Fattail.CreativeManagement.API.Domain.CreativeFiles;

namespace Fattail.CreativeManagement.API.Application.Creatives;

public abstract record CreativeFieldDto (
    long Id,
    CreativeFieldType Type)
{
    internal abstract object? GetValue ();
}

public abstract record CreativeFieldValue<TValue> (
    long Id,
    CreativeFieldType Type,
    TValue Value) : CreativeFieldDto(Id, Type);

public sealed record MultiFileUploadFieldValue (
        long Id,
        IReadOnlyList<long> Value)
    : CreativeFieldValue<IReadOnlyList<long>>(Id, CreativeFieldType.MultiFileUpload, Value)
{
    internal override object? GetValue ()
    {
        return Value.Select(creativeFileId => new CreativeFileId(creativeFileId));
    }
}

public sealed record FileUploadFieldValue (
        long Id,
        long Value)
    : CreativeFieldValue<long>(Id, CreativeFieldType.FileUpload, Value)
{
    internal override object? GetValue ()
    {
        return Value > 0 ? (CreativeFileId)Value : null;
    }
}

public sealed record SingleLineTextFieldValue (
        long Id,
        string Value)
    : CreativeFieldValue<string>(Id, CreativeFieldType.SingleLineText, Value)
{
    internal override object? GetValue ()
    {
        return Value;
    }
}

public sealed record MultiSelectOptionFieldValue (
        long Id,
        IReadOnlyList<long> Value)
    : CreativeFieldValue<IReadOnlyList<long>>(Id, CreativeFieldType.MultiSelectOption, Value)
{
    internal override object? GetValue ()
    {
        return Value;
    }
}

public sealed record SingleSelectOptionFieldValue (
        long Id,
        long? Value)
    : CreativeFieldValue<long?>(Id, CreativeFieldType.SingleSelectOption, Value)
{
    internal override object? GetValue ()
    {
        return Value;
    }
}

public sealed record MultiLineTextFieldValue (
        long Id,
        string Value)
    : CreativeFieldValue<string>(Id, CreativeFieldType.MultiLineText, Value)
{
    internal override object? GetValue ()
    {
        return Value;
    }
}