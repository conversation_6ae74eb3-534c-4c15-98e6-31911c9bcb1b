using AutoMapper;
using Fattail.CreativeManagement.API.Domain.Repositories;
using Fattail.CreativeManagement.API.Domain.CreativeFields;
using Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Interfaces;
using Microsoft.Azure.Cosmos;
using Microsoft.Azure.Cosmos.Linq;
using System.Net;
using Fattail.CreativeManagement.API.Application;
using Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Settings;
using Microsoft.Extensions.Options;

namespace Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Repositories;

internal sealed class CreativeFieldRepository :
    CosmosDbRepository<CreativeField, CreativeFieldId, Entities.CreativeField>,
    ICreativeFieldRepository
{
    public CreativeFieldRepository (
        ICosmosDbContainerFactory cosmosDbContainerFactory,
        IMapper mapper,
        IOrganizationContext organizationContext,
        IOptions<ParallelExecutionSettings> parallelExecutionSettings,
        ILogger<CreativeFieldRepository> log) : base(cosmosDbContainerFactory, mapper,
        organizationContext, parallelExecutionSettings, log)
    {
    }

    public override string ContainerName => "CreativeFields";

    public override PartitionKey ResolvePartitionKey ()
    {
        return new PartitionKey($"{_organizationContext.OrganizationId}");
    }

    public async Task<IReadOnlyList<CreativeField>> GetAllAsync ()
    {
        var items = new List<CreativeField>();

        try
        {
            IQueryable<Entities.CreativeField>? feedResponse =
                _container.GetItemLinqQueryable<Entities.CreativeField>();

            using (var resultSet = feedResponse.ToFeedIterator())
            {
                while (resultSet.HasMoreResults)
                {
                    FeedResponse<Entities.CreativeField> response = await _retryPolicy.ExecuteAsync(() => resultSet.ReadNextAsync());
                    items.AddRange(_mapper.Map<IReadOnlyList<CreativeField>>(response.Resource));
                }
            }

            return items;
        }
        catch (CosmosException ex) when (ex.StatusCode == HttpStatusCode.NotFound)
        {
            return new List<CreativeField>();
        }

    }
}