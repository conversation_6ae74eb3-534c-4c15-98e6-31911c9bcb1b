using Fattail.CreativeManagement.API.Domain.Common.Errors;
using Fattail.CreativeManagement.API.Domain.CreativeFields;
using Fattail.CreativeManagement.API.Domain.Creatives;
using Fattail.CreativeManagement.API.Domain.Creatives.Fields;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates.Fields;
using FluentAssertions;
using FluentResults;
using FluentResults.Extensions.FluentAssertions;
using NUnit.Framework;
using static FluentAssertions.FluentActions;

namespace Fattail.CreativeManagement.API.Tests.Domain.Creatives.Fields;

[TestFixture]
public class SingleSelectOptionFieldValueTests
{
    private static readonly CreativeFieldType[] _invalidCreativeFieldTypes =
        ((CreativeFieldType[])Enum.GetValues(typeof(CreativeFieldType)))
        .Where(creativeFieldType => creativeFieldType != CreativeFieldType.SingleSelectOption)
        .ToArray();

    [Test]
    public async Task Single_select_option_field_value_can_be_created ()
    {
        var creativeField = CreativeFieldIdentifier.Create(new CreativeFieldId(12345),
            CreativeFieldType.SingleSelectOption);

        long optionId = 111;

        Result<SingleSelectOptionFieldValue> singleSelectOptionFieldValue =
            await SingleSelectOptionFieldValue.Create(creativeField, optionId, new List<ValidationRule>());

        singleSelectOptionFieldValue.Should().BeSuccess();
        singleSelectOptionFieldValue.Value.CreativeFieldIdentifier.Should().Be(creativeField);
        singleSelectOptionFieldValue.Value.Value.Should().Be(optionId);
    }

    [Test]
    public async Task Single_select_option_field_value_can_be_created_without_value ()
    {
        var creativeField = CreativeFieldIdentifier.Create(new CreativeFieldId(12345),
            CreativeFieldType.SingleSelectOption);

        var singleSelectOptionFieldValueWithoutValue =
            (SingleSelectOptionFieldValue)(await CreativeFieldValue.CreateWithoutValue(creativeField, new List<ValidationRule>())).Value;

        singleSelectOptionFieldValueWithoutValue.CreativeFieldIdentifier.Should().Be(creativeField);
        singleSelectOptionFieldValueWithoutValue.Value.Should().BeNull();
    }

    [Test]
    public async Task Single_select_option_field_value_with_required_validation_and_valid_value_succeeds ()
    {
        var creativeField = CreativeFieldIdentifier.Create(new CreativeFieldId(12345),
            CreativeFieldType.SingleSelectOption);

        var validationRules = new List<ValidationRule>
        {
            ValidationRule.Create(CreativeFieldValidationRuleType.Required, new List<string>(), new CreativeFieldId(12345)).Value
        };

        long optionId = 111;

        Result<SingleSelectOptionFieldValue> singleSelectOptionFieldValue =
            await SingleSelectOptionFieldValue.Create(creativeField, optionId, validationRules);

        singleSelectOptionFieldValue.Should().BeSuccess();
        singleSelectOptionFieldValue.Value.CreativeFieldIdentifier.Should().Be(creativeField);
        singleSelectOptionFieldValue.Value.Value.Should().Be(optionId);
    }

    [Test]
    public async Task Single_select_option_field_value_with_required_validation_and_null_value_fails ()
    {
        var creativeField = CreativeFieldIdentifier.Create(new CreativeFieldId(12345),
            CreativeFieldType.SingleSelectOption);

        var validationRules = new List<ValidationRule>
        {
            ValidationRule.Create(CreativeFieldValidationRuleType.Required, new List<string>(), new CreativeFieldId(12345)).Value
        };

        Result<SingleSelectOptionFieldValue> singleSelectOptionFieldValue =
            await SingleSelectOptionFieldValue.Create(creativeField, null, validationRules);

        singleSelectOptionFieldValue.Should().BeFailure().And
            .HaveReason(new RequiredValidationError(nameof(CreativeFieldValue), nameof(Creative),
                creativeField.Id.ToString()));
    }

    [Test]
    public async Task Single_select_option_field_value_with_required_validation_and_zero_value_fails ()
    {
        var creativeField = CreativeFieldIdentifier.Create(new CreativeFieldId(12345),
            CreativeFieldType.SingleSelectOption);

        var validationRules = new List<ValidationRule>
        {
            ValidationRule.Create(CreativeFieldValidationRuleType.Required, new List<string>(), new CreativeFieldId(12345)).Value
        };

        Result<SingleSelectOptionFieldValue> singleSelectOptionFieldValue =
            await SingleSelectOptionFieldValue.Create(creativeField, 0L, validationRules);

        singleSelectOptionFieldValue.Should().BeFailure().And
            .HaveReason(new RequiredValidationError(nameof(CreativeFieldValue), nameof(Creative),
                creativeField.Id.ToString()));
    }

    [Test]
    public async Task Single_select_option_field_value_with_required_validation_and_negative_value_fails ()
    {
        var creativeField = CreativeFieldIdentifier.Create(new CreativeFieldId(12345),
            CreativeFieldType.SingleSelectOption);

        var validationRules = new List<ValidationRule>
        {
            ValidationRule.Create(CreativeFieldValidationRuleType.Required, new List<string>(), new CreativeFieldId(12345)).Value
        };

        Result<SingleSelectOptionFieldValue> singleSelectOptionFieldValue =
            await SingleSelectOptionFieldValue.Create(creativeField, -1L, validationRules);

        singleSelectOptionFieldValue.Should().BeFailure().And
            .HaveReason(new RequiredValidationError(nameof(CreativeFieldValue), nameof(Creative),
                creativeField.Id.ToString()));
    }

    [Test]
    public async Task Single_select_option_field_value_creation_with_invalid_value_type_fails ()
    {
        var creativeField = CreativeFieldIdentifier.Create(new CreativeFieldId(12345),
            CreativeFieldType.SingleSelectOption);

        Result<SingleSelectOptionFieldValue> result =
            await SingleSelectOptionFieldValue.Create(creativeField, "invalid_long", new List<ValidationRule>());

        result.Should().BeFailure().And
            .HaveReason(new InvalidValueError("The value 'invalid_long' is not a valid long integer.",
                nameof(SingleSelectOptionFieldValue)));
    }

    [TestCaseSource(nameof(_invalidCreativeFieldTypes))]
    public async Task Single_select_option_field_value_creation_with_invalid_creative_field_type_fails (
        CreativeFieldType invalidCreativeFieldType)
    {
        var creativeField = CreativeFieldIdentifier.Create(new CreativeFieldId(12345), invalidCreativeFieldType);

        await Invoking(async () =>
                await SingleSelectOptionFieldValue.Create(creativeField, 111L, null))
            .Should().ThrowExactlyAsync<ArgumentException>();
    }

    [Test]
    public async Task Single_select_option_field_value_generates_new_value ()
    {
        var creativeField = CreativeFieldIdentifier.Create(new CreativeFieldId(12345),
            CreativeFieldType.SingleSelectOption);

        var singleSelectOptionFieldValueWithoutValue =
            (SingleSelectOptionFieldValue)(await CreativeFieldValue.CreateWithoutValue(creativeField, new List<ValidationRule>())).Value;

        long optionId = 111;

        var singleSelectOptionFieldValueWithNewValue =
            (SingleSelectOptionFieldValue)(await singleSelectOptionFieldValueWithoutValue.GenerateNewValue(optionId, new List<ValidationRule>())).Value;

        singleSelectOptionFieldValueWithNewValue.CreativeFieldIdentifier.Should().Be(creativeField);
        singleSelectOptionFieldValueWithNewValue.Value.Should().Be(optionId);
    }
}
