﻿using AutoMapper;
using AutoMapper.QueryableExtensions;
using Fattail.CreativeManagement.API.Domain.Repositories;
using Fattail.CreativeManagement.API.Domain.Common;
using Fattail.CreativeManagement.API.Domain.Common.Errors;
using Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Entities;
using Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Entities.Enums;
using Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Interfaces;
using FluentResults;
using Microsoft.Azure.Cosmos;
using Microsoft.Azure.Cosmos.Linq;
using System.Linq.Expressions;
using System.Net;
using Fattail.CreativeManagement.API.Application;
using Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Settings;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Polly;
using Polly.Retry;

namespace Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Repositories;

internal abstract class
    CosmosDbRepository<TDomainEntity, TDomainEntityId, TEntity> : IRepository<TDomainEntity, TDomainEntityId>,
    IContainerContext
    where TDomainEntity : Entity<TDomainEntityId>
    where TEntity : Entity
{
    protected readonly Container _container;
    protected readonly IMapper _mapper;
    protected readonly IOrganizationContext _organizationContext;
    protected readonly ParallelExecutionSettings _parallelExecutionSettings;
    protected readonly AsyncRetryPolicy _retryPolicy;
    protected readonly ILogger _log;

    protected CosmosDbRepository (
        ICosmosDbContainerFactory cosmosDbContainerFactory,
        IMapper mapper,
        IOrganizationContext organizationContext,
        IOptions<ParallelExecutionSettings> parallelExecutionSettings,
        ILogger log)
    {
        _container = cosmosDbContainerFactory.GetContainer(ContainerName).Container;
        _mapper = mapper;
        _organizationContext = organizationContext;
        _parallelExecutionSettings = parallelExecutionSettings.Value;
        _log = log;
        _retryPolicy = Policy
            .Handle<CosmosException>(ex =>
                ex.StatusCode is HttpStatusCode.TooManyRequests
                    or HttpStatusCode.RequestTimeout
                    or HttpStatusCode.ServiceUnavailable)
            .WaitAndRetryAsync(4, _ => TimeSpan.FromSeconds(Math.Pow(2, _)));
    }

    public abstract string ContainerName { get; }

    public abstract PartitionKey ResolvePartitionKey ();

    public virtual async Task<TDomainEntity?> FindByIdAsync (TDomainEntityId id)
    {
        try
        {
            ItemResponse<TEntity> response = await _retryPolicy.ExecuteAsync(() =>
                _container.ReadItemAsync<TEntity>(id?.ToString(), ResolvePartitionKey()));
            return _mapper.Map<TDomainEntity>(response.Resource);
        }
        catch (CosmosException ex) when (ex.StatusCode == HttpStatusCode.NotFound)
        {
            return default;
        }
    }

    public async Task<TResult?> FindByIdAsync<TResult> (TDomainEntityId id)
    {
        try
        {
            ItemResponse<TResult?> response = await _retryPolicy.ExecuteAsync(() =>
                _container.ReadItemAsync<TResult?>(id?.ToString(), ResolvePartitionKey()));
            return response.Resource;
        }
        catch (CosmosException ex) when (ex.StatusCode == HttpStatusCode.NotFound)
        {
            return default;
        }
    }

    public async Task<TDomainEntity?> FindAsync (Expression<Func<TDomainEntity, bool>> predicate)
    {
        try
        {
            IQueryable<TEntity> dbQuery = BuildQueryFromPredicate(predicate);

            using var resultSet = dbQuery.ToFeedIterator();
            while (resultSet.HasMoreResults)
            {
                FeedResponse<TEntity> response = await _retryPolicy.ExecuteAsync(() => resultSet.ReadNextAsync());
                return response.Any() ? _mapper.Map<TDomainEntity>(response.Resource.First()) : null;
            }
        }
        catch (CosmosException ex) when (ex.StatusCode == HttpStatusCode.NotFound)
        {
        }

        return default;
    }

    public async Task<TDomainEntity?> FindAsync (Specification<TDomainEntity> specification)
    {
        try
        {
            IQueryable<TEntity> dbQuery = BuildQueryFromPredicate(specification.ToExpression());

            using var resultSet = dbQuery.ToFeedIterator();
            while (resultSet.HasMoreResults)
            {
                FeedResponse<TEntity> response = await _retryPolicy.ExecuteAsync(() => resultSet.ReadNextAsync());
                return response.Any() ? _mapper.Map<TDomainEntity>(response.Resource.First()) : null;
            }
        }
        catch (CosmosException ex) when (ex.StatusCode == HttpStatusCode.NotFound)
        {
        }

        return null;
    }

    public async Task<IReadOnlyList<TDomainEntity>> FindManyAsync (Expression<Func<TDomainEntity, bool>> predicate)
    {
        var items = new List<TDomainEntity>();
        IQueryable<TEntity> dbQuery = BuildQueryFromPredicate(predicate);

        using var resultSet = dbQuery.ToFeedIterator();
        while (resultSet.HasMoreResults)
        {
            FeedResponse<TEntity> response = await _retryPolicy.ExecuteAsync(() => resultSet.ReadNextAsync());
            items.AddRange(_mapper.Map<List<TDomainEntity>>(response.Resource));
        }

        return items;
    }

    private IQueryable<TEntity> BuildQueryFromPredicate (Expression<Func<TDomainEntity, bool>> predicate)
    {
        return _container
            .GetItemLinqQueryable<TDomainEntity>(
                true,
                requestOptions: new QueryRequestOptions { PartitionKey = ResolvePartitionKey() },
                linqSerializerOptions: new CosmosLinqSerializerOptions()
                {
                    PropertyNamingPolicy = CosmosPropertyNamingPolicy.CamelCase
                })
            .Where(predicate).ProjectTo<TEntity>(_mapper.ConfigurationProvider);
    }

    public virtual async Task<IReadOnlyList<TResult>> FindManyByIdAsync<TResult> (IEnumerable<TDomainEntityId> ids)
    {
        try
        {
            IEnumerable<TEntity> cosmosEntities = await FindManyInCosmosByIdAsync<TResult>(ids);
            return _mapper.Map<IReadOnlyList<TResult>>(cosmosEntities);
        }
        catch (CosmosException ex) when (ex.StatusCode == HttpStatusCode.NotFound)
        {
            return new List<TResult>();
        }
    }

    protected async Task<IEnumerable<TEntity>> FindManyInCosmosByIdAsync<TResult> (IEnumerable<TDomainEntityId> ids)
    {
        IReadOnlyList<(string?, PartitionKey)> itemList =
            ids.Select(id => (id?.ToString(), ResolvePartitionKey())).ToList();

        FeedResponse<TEntity> feedResponse = await _retryPolicy.ExecuteAsync(() =>
            _container.ReadManyItemsAsync<TEntity>(itemList));

        return feedResponse.Resource;
    }

    public async Task CreateAsync (TDomainEntity entity)
    {
        await CreateEntityAsync(entity);
    }

    public async Task<TResult> CreateAsync<TResult> (TDomainEntity entity)
    {
        ItemResponse<TEntity>? response = await _retryPolicy.ExecuteAsync(() =>
            CreateEntityAsync(entity));
        return _mapper.Map<TResult>(response.Resource);
    }

    public async Task<TResult> MigrateAsync<TResult> (TDomainEntity entity)
    {
        ItemResponse<TEntity>? response = await _retryPolicy.ExecuteAsync(() =>
            MigrateEntityAsync(entity));
        return _mapper.Map<TResult>(response.Resource);
    }

    public virtual async Task<TResult> UpdateAsync<TResult> (TDomainEntity entity)
    {
        TEntity cosmosEntity = PrepareCosmosEntity(entity, ActionType.Update);
        ItemResponse<TEntity> response = await _retryPolicy.ExecuteAsync(() =>
            _container.UpsertItemAsync(cosmosEntity, ResolvePartitionKey()));
        return _mapper.Map<TResult>(response.Resource);
    }

    public async Task<Result> DeleteAsync (TDomainEntityId id)
    {
        try
        {
            ItemResponse<TDomainEntity>? res = await _retryPolicy.ExecuteAsync(() =>
                _container.DeleteItemAsync<TDomainEntity>(id.ToString(), ResolvePartitionKey()));
            return Result.Ok();
        }
        catch (CosmosException ex) when (ex.StatusCode == HttpStatusCode.NotFound)
        {
            return Result.Fail(new EntityNotFoundError(id.ToString(), nameof(TDomainEntity)));
        }
    }

    protected virtual async Task<ItemResponse<TEntity>> CreateEntityAsync (TDomainEntity entity)
    {
        TEntity cosmosEntity = PrepareCosmosEntity(entity, ActionType.Create);
        return await _container.CreateItemAsync(cosmosEntity, ResolvePartitionKey());
    }

    protected virtual async Task<ItemResponse<TEntity>> MigrateEntityAsync (TDomainEntity entity)
    {
        TEntity cosmosEntity = PrepareCosmosEntity(entity, ActionType.Migrate);
        return await _container.UpsertItemAsync(cosmosEntity, ResolvePartitionKey());
    }

    protected async Task UpdateCosmosEntityAsync (Entities.CreativeFile cosmosEntity)
    {
        try
        {
            cosmosEntity.LastAction = ActionType.Update;
            await _retryPolicy.ExecuteAsync(() =>
                _container.ReplaceItemAsync(cosmosEntity, cosmosEntity.Id, ResolvePartitionKey()));
        }
        catch (CosmosException ex)
        {
            _log.LogError(ex, "Attempt to update the cosmos entity failed - {Type} - {Id}", nameof(Entities.CreativeFile), cosmosEntity.Id);
        }
    }

    protected TEntity PrepareCosmosEntity (TDomainEntity entity, ActionType actionType)
    {
        TEntity? cosmosEntity = _mapper.Map<TEntity>(entity);

        cosmosEntity.LastAction = actionType;
        cosmosEntity.OrgId = _organizationContext.OrganizationId.ToString();
        return cosmosEntity;
    }
}