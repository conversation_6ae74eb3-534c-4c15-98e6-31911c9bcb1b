﻿using Fattail.CreativeManagement.API.Domain.CreativeFields;
using Fattail.CreativeManagement.API.Domain.CreativeFiles;
using Fattail.CreativeManagement.API.Domain.Creatives.Fields;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates.Fields;
using FluentAssertions;
using FluentResults;
using NUnit.Framework;
using static FluentAssertions.FluentActions;

namespace Fattail.CreativeManagement.API.Tests.Domain.Creatives.Fields;

[TestFixture]
public class MultiLineTextFieldValueTests
{
    private static readonly CreativeFieldType[] _invalidCreativeFieldTypes =
        ((CreativeFieldType[])Enum.GetValues(typeof(CreativeFieldType)))
        .Where(creativeFieldType => creativeFieldType != CreativeFieldType.MultiLineText)
        .ToArray();

    [Test]
    public async Task Multi_line_text_field_value_can_be_created ()
    {
        var creativeField = CreativeFieldIdentifier.Create(new CreativeFieldId(12345),
            CreativeFieldType.MultiLineText);

        string text = "Multi line text field test value";

        Result<MultiLineTextFieldValue> multiLineTextFieldValue = await MultiLineTextFieldValue.Create(creativeField, text, new List<ValidationRule>());

        multiLineTextFieldValue.Value.CreativeFieldIdentifier.Should().Be(creativeField);
        multiLineTextFieldValue.Value.Value.Should().Be(text);
    }

    [Test]
    public async Task Multi_line_text_field_value_can_be_created_without_value ()
    {
        var creativeField = CreativeFieldIdentifier.Create(new CreativeFieldId(12345),
            CreativeFieldType.MultiLineText);

        var multiLineTextFieldValueWithoutValue =
            (MultiLineTextFieldValue)(await CreativeFieldValue.CreateWithoutValue(creativeField, new List<ValidationRule>())).Value;

        multiLineTextFieldValueWithoutValue.CreativeFieldIdentifier.Should().Be(creativeField);
        multiLineTextFieldValueWithoutValue.Value.Should().BeNull();
    }

    [Test]
    public async Task Multi_line_text_field_value_creation_with_invalid_value_type_fails ()
    {
        var creativeField = CreativeFieldIdentifier.Create(new CreativeFieldId(12345),
            CreativeFieldType.MultiLineText);

        await Invoking(async () =>
               await MultiLineTextFieldValue.Create(creativeField, new object(), null))
            .Should().ThrowExactlyAsync<ArgumentException>();
    }

    [TestCaseSource(nameof(_invalidCreativeFieldTypes))]
    public async Task Multi_line_text_field_value_creation_with_invalid_creative_field_type_fails (
        CreativeFieldType invalidCreativeFieldType)
    {
        var creativeField = CreativeFieldIdentifier.Create(new CreativeFieldId(12345), invalidCreativeFieldType);

        await Invoking(async () =>
                await MultiLineTextFieldValue.Create(creativeField,
                    new CreativeFileId(12345), null))
            .Should().ThrowExactlyAsync<ArgumentException>();
    }

    [Test]
    public async Task Multi_line_text_field_value_generates_new_value ()
    {
        var creativeField = CreativeFieldIdentifier.Create(new CreativeFieldId(12345),
            CreativeFieldType.MultiLineText);

        var multiLineTextFieldValueWithoutValue =
            (MultiLineTextFieldValue)(await CreativeFieldValue.CreateWithoutValue(creativeField, new List<ValidationRule>())).Value;

        string text = "Multi line text field test value";

        var multiLineTextFieldValueWithNewValue =
            (MultiLineTextFieldValue)(await multiLineTextFieldValueWithoutValue.GenerateNewValue(text, new List<ValidationRule>())).Value;

        multiLineTextFieldValueWithoutValue.Value.Should().BeNull();
        multiLineTextFieldValueWithoutValue.CreativeFieldIdentifier.Should().Be(creativeField);

        multiLineTextFieldValueWithNewValue.CreativeFieldIdentifier.Should().Be(creativeField);
        multiLineTextFieldValueWithNewValue.Value.Should().Be(text);
    }
}