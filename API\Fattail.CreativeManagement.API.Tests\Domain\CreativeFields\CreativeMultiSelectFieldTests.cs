﻿using Fattail.CreativeManagement.API.Domain.Common;
using Fattail.CreativeManagement.API.Domain.Common.Errors;
using Fattail.CreativeManagement.API.Domain.CreativeFields;
using Fattail.CreativeManagement.API.Domain.CreativeFields.Errors;
using FluentAssertions;
using FluentResults;
using FluentResults.Extensions.FluentAssertions;
using Moq;
using NUnit.Framework;

namespace Fattail.CreativeManagement.API.Tests.Domain.CreativeFields;

[TestFixture]
public class CreativeMultiSelectFieldTests
{
    [TestCase("")]
    [TestCase(" ")]
    [TestCase(null)]
    public void Creative_multiselect_field_cant_be_created_without_name (string? name)
    {
        var creativeFieldUniqueNameRequirement = new CreativeFieldUniqueNameRequirement(true);

        Result<CreativeField> creativeFieldResult = MultiSelectCreativeField.Create(new (1234),
            name,
            CreativeFieldType.FileUpload,
            new List<SelectOption>(),
            creativeFieldUniqueNameRequirement, false, null);

        creativeFieldResult.IsFailed.Should().BeTrue();
        creativeFieldResult.Should().BeFailure()
            .And.HaveReason(new RequiredValueMissingError("creative field name", nameof(CreativeField)));
    }
    
    [Test]
    public void Creative_multiselect_field_cant_be_created_when_unique_name_requirement_is_not_satisfied ()
    {
        string creativeFieldName = "Creative field name";
        CreativeFieldType creativeFieldType = CreativeFieldType.MultiFileUpload;
        var creativeFieldUniqueNameRequirement = new CreativeFieldUniqueNameRequirement(false);


        Result<CreativeField> creativeFieldResult = CreativeField.Create(new (1234),
            creativeFieldName,
            creativeFieldType,
            creativeFieldUniqueNameRequirement, false, null);
        
        creativeFieldResult.Should().BeFailure()
            .And.HaveReason(new CreativeFieldNameInUseError(creativeFieldName, creativeFieldType.ToString(), nameof(CreativeField)));
    }
    
    [Test]
    public void Creative_multiselect_field_can_be_created ()
    {
        string creativeFieldName = "Creative field name";
        var creativeFieldUniqueNameRequirement = new CreativeFieldUniqueNameRequirement(true);
        CreativeFieldType creativeFieldType = CreativeFieldType.MultiFileUpload;

        Result<CreativeField> creativeTemplateResult = CreativeField.Create(new (1234),
            creativeFieldName,
            creativeFieldType,
            creativeFieldUniqueNameRequirement, false, null);

        creativeTemplateResult.Should().BeSuccess();
        creativeTemplateResult.Value.Should().NotBeNull();
        creativeTemplateResult.Value.Name.Should().BeEquivalentTo(creativeFieldName);
        creativeTemplateResult.Value.Type.Should().Be(creativeFieldType);
    }
}