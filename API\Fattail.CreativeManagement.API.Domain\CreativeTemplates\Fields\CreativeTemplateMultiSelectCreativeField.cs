﻿using Fattail.CreativeManagement.API.Domain.CreativeFields;

namespace Fattail.CreativeManagement.API.Domain.CreativeTemplates.Fields
{
    public class CreativeTemplateMultiSelectCreativeField : CreativeTemplateCreativeField
    {
        internal CreativeTemplateMultiSelectCreativeField (CreativeFieldId id, string name, CreativeFieldType type, IReadOnlyList<SelectOption> options, Dictionary<CreativeFieldValidationRuleType, ValidationRule> validationRules, int displayOrder, string? tooltip) 
            : base(id, name, type, validationRules, displayOrder, tooltip)
        {
            Options = options;
        }

        public IReadOnlyList<SelectOption> Options { get; }
    }
}
