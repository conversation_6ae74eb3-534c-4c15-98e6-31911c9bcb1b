﻿using Dawn;
using Fattail.CreativeManagement.API.Domain.CreativeFields;
using Fattail.CreativeManagement.API.Domain.CreativeFiles;
using Fattail.CreativeManagement.API.Domain.Creatives.Fields.Guards;
using Fattail.CreativeManagement.API.Domain.Creatives.Fields.Sanitizers.StrategyProvider;
using Fattail.CreativeManagement.API.Domain.Creatives.Fields.Validators;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates.Fields;
using FluentResults;

namespace Fattail.CreativeManagement.API.Domain.Creatives.Fields;

public sealed class FileUploadFieldValue : CreativeFieldValue<CreativeFileId>
{
    public FileUploadFieldValue (CreativeFieldIdentifier creativeFieldIdentifier, CreativeFileId? value) : base(
        creativeFieldIdentifier, value)
    {
    }

    internal static async Task<Result<FileUploadFieldValue>> Create (
        ISanitizerStrategyProvider? sanitizerStrategyProvider,
        CreativeFieldIdentifier creativeFieldIdentifier, object? value, IEnumerable<ValidationRule> validationRules)
    {
        Guard.Argument(creativeFieldIdentifier, nameof(creativeFieldIdentifier))
            .FieldTypeIs(CreativeFieldType.FileUpload);

        IReadOnlyList<CreativeFile?> creativeFiles = null;
        CreativeFile? creativeFile = null;

        if (value is not null)
        {
            Guard.Argument(value, nameof(value)).Compatible<CreativeFileId>();

            Guard.Argument(sanitizerStrategyProvider, nameof(sanitizerStrategyProvider))
                .NotNull("Sanitizer factory must be provided in order to create a file upload field with value");

            creativeFile = await sanitizerStrategyProvider!.GetFrom(CreativeFieldType.FileUpload)
                .Sanitize<CreativeFile>(value);

            if (creativeFile != null)
            {
                creativeFiles = new List<CreativeFile> { creativeFile };
            }
        }

        Result validationResults = await CreativeFieldValueValidationManager.Validate(creativeFiles, validationRules,
            creativeFieldIdentifier);

        return validationResults.IsFailed
            ? validationResults
            : new FileUploadFieldValue(creativeFieldIdentifier, creativeFile?.Id);
    }

    public override async Task<Result<CreativeFieldValue>> GenerateNewValue (object? value,
        IEnumerable<ValidationRule> validationRules,
        ISanitizerStrategyProvider? sanitizerStrategyProvider = null)
    {
        return (await Create(sanitizerStrategyProvider, CreativeFieldIdentifier, value, validationRules))
            .ToResult<CreativeFieldValue>(creativeFieldValue => creativeFieldValue);
    }
}