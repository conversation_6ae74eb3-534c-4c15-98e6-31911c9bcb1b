﻿using Fattail.CreativeManagement.API.Domain.Common.Errors;
using FluentResults;

namespace Fattail.CreativeManagement.API.Domain.Creatives.Fields.Validators.RequiredValidators;

public class CollectionsRequiredRuleValidator<T, TItem> : IRuleValidator<T> where T : IEnumerable<TItem>?
{
    private readonly string _creativeFieldId;
    public CollectionsRequiredRuleValidator (CreativeFieldIdentifier creativeFieldIdentifier)
    {
        _creativeFieldId = creativeFieldIdentifier.Id.ToString();
    }

    public Result IsValid (T fieldValue)
    {
        var result = Result.Ok();

        if (fieldValue is null || !fieldValue.Any())
        {
            result = Result.Fail(new RequiredValidationError(nameof(CreativeFieldValue), nameof(Creative),
                _creativeFieldId));
        }

        return result;
    }
}