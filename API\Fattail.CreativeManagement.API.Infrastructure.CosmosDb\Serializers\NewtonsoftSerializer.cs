﻿using Fattail.CreativeManagement.API.Domain.CreativeFields;
using Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Entities;
using JsonSubTypes;
using Microsoft.Azure.Cosmos;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using Newtonsoft.Json.Serialization;
using System.Text;
using CreativeField = Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Entities.CreativeField;
using MultiSelectCreativeField = Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Entities.MultiSelectCreativeField;
using SingleSelectCreativeField = Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Entities.SingleSelectCreativeField;

namespace Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Serializers;

public sealed class NewtonsoftSerializer : CosmosSerializer
{
    private static readonly Encoding _defaultEncoding = new UTF8Encoding(false, true);
    private readonly JsonSerializer _jsonSerializer;

    public NewtonsoftSerializer ()
    {
        var serializerSettings = new JsonSerializerSettings
        {
            ContractResolver = new CamelCasePropertyNamesContractResolver(),
            NullValueHandling = NullValueHandling.Include,
        };
        
        serializerSettings.Converters.Add(new StringEnumConverter());

        serializerSettings.Converters.Add(JsonSubtypesConverterBuilder.Of<CreativeFieldValue>("Type")
            .RegisterSubtype<MultiFileUploadFieldValue>(CreativeFieldType.MultiFileUpload)
            .RegisterSubtype<FileUploadFieldValue>(CreativeFieldType.FileUpload)
            .RegisterSubtype<SingleLineTextFieldValue>(CreativeFieldType.SingleLineText)
            .RegisterSubtype<MultiSelectOptionFieldValue>(CreativeFieldType.MultiSelectOption)
            .RegisterSubtype<SingleSelectOptionFieldValue>(CreativeFieldType.SingleSelectOption)
            .RegisterSubtype<MultiLineTextFieldValue>(CreativeFieldType.MultiLineText)
            .Build());

        serializerSettings.Converters.Add(JsonSubtypesConverterBuilder.Of<CreativeTemplateCreativeField>("Type")
            .RegisterSubtype<CreativeTemplateMultiSelectCreativeField>(CreativeFieldType.MultiSelectOption)
            .RegisterSubtype<CreativeTemplateSingleSelectCreativeField>(CreativeFieldType.SingleSelectOption)
            .RegisterSubtype<CreativeTemplateSectionDividerCreativeField>(CreativeFieldType.SectionDivider)
           .Build());

        serializerSettings.Converters.Add(JsonSubtypesConverterBuilder.Of<CreativeField>("Type")
           .RegisterSubtype<MultiSelectCreativeField>(CreativeFieldType.MultiSelectOption)
           .RegisterSubtype<SingleSelectCreativeField>(CreativeFieldType.SingleSelectOption)
           .Build());

        _jsonSerializer = JsonSerializer.Create(serializerSettings);
    }

    public override T FromStream<T> (Stream stream)
    {
        using (stream)
        {
            if (typeof(Stream).IsAssignableFrom(typeof(T)))
            {
                return (T)(object)stream;
            }

            using (var sr = new StreamReader(stream))
            {
                using (var jsonTextReader = new JsonTextReader(sr))
                {
                    return _jsonSerializer.Deserialize<T>(jsonTextReader)!;
                }
            }
        }
    }

    public override Stream ToStream<T> (T input)
    {
        var streamPayload = new MemoryStream();
        using (var streamWriter = new StreamWriter(streamPayload, _defaultEncoding, 1024, true))
        {
            using (JsonWriter writer = new JsonTextWriter(streamWriter))
            {
                writer.Formatting = Formatting.None;
                _jsonSerializer.Serialize(writer, input);
                writer.Flush();
                streamWriter.Flush();
            }
        }

        streamPayload.Position = 0;
        return streamPayload;
    }
}