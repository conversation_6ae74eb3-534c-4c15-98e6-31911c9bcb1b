﻿using Fattail.CreativeManagement.API.Domain.Common.Errors;
using Fattail.CreativeManagement.API.Domain.CreativeFiles;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates.Fields;
using FluentResults;

namespace Fattail.CreativeManagement.API.Domain.Creatives.Fields.Validators;

public class FileSizeRuleValidator : IRuleValidator<IReadOnlyList<CreativeFile?>>
{
    private readonly double _allowedFileSize;
    private readonly string _creativeFieldId;

    public FileSizeRuleValidator (IEnumerable<ValidationRule> validationRules,
        CreativeFieldIdentifier creativeFieldIdentifier)
    {
        ValidationRule fileSizeValidationRule = validationRules.Single(validationRule =>
            validationRule.Type == CreativeFieldValidationRuleType.FileSize);
        _creativeFieldId = creativeFieldIdentifier.Id.ToString();
        string fileSize = fileSizeValidationRule.Options.Select(option => option).Single();

        _allowedFileSize = double.Parse(fileSize);
    }

    private bool CreativeFileSizeIsValid (CreativeFile creativeFile)
    {
        double creativeFileSize = creativeFile.Size.SizeInMegaBytes;

        return creativeFileSize <= _allowedFileSize;
    }

    public Result IsValid (IReadOnlyList<CreativeFile?> creativeFiles)
    {
        var result = Result.Ok();

        if (creativeFiles != null && creativeFiles.Any() && _allowedFileSize > 0)
        {
            foreach (CreativeFile? creativeFile in creativeFiles)
            {
                if (creativeFile != null && !CreativeFileSizeIsValid(creativeFile))
                {
                    result = Result.Merge(result, Result.Fail(new FileSizeValidationError(nameof(CreativeFieldValue), nameof(Creative), _creativeFieldId, _allowedFileSize.ToString())));
                    break;
                }
            }
        }

        return result;
    }
}