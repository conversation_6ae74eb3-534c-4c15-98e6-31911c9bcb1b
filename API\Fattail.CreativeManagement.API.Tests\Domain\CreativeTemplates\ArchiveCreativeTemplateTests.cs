using Fattail.CreativeManagement.API.Domain.CreativeFields;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates.Errors;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates.Factory;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates.Requirements;
using FluentAssertions;
using FluentResults;
using FluentResults.Extensions.FluentAssertions;
using NUnit.Framework;

namespace Fattail.CreativeManagement.API.Tests.Domain.CreativeTemplates;

[TestFixture]
public class ArchiveCreativeTemplateTests
{
    private readonly CreativeFieldUniqueNameRequirement _creativeFieldUniqueNameRequirement = new(true);

    [Test]
    public void Creative_template_can_be_archived_when_not_predefined()
    {
        CreativeTemplate template = CreateTestTemplate(123, "Test Template", CreativeType.Image, false);

        Result result = template.Archive();

        result.Should().BeSuccess();
        template.Archived.Should().BeTrue();
    }

    [Test]
    public void Creative_template_cannot_be_archived_when_predefined()
    {
        CreativeTemplate predefinedTemplate = CreateTestTemplate(123, "Predefined Template", CreativeType.Image, true);

        Result result = predefinedTemplate.Archive();

        result.Should().BeFailure()
            .And.HaveReason<PredefinedCreativeTemplateCannotBeArchivedError>(null);
        predefinedTemplate.Archived.Should().BeFalse();
    }

    [Test]
    public void Creative_template_can_be_archived_multiple_times_when_not_predefined()
    {
        CreativeTemplate template = CreateTestTemplate(123, "Test Template", CreativeType.Image, false);

        Result firstResult = template.Archive();
        Result secondResult = template.Archive();

        firstResult.Should().BeSuccess();
        secondResult.Should().BeSuccess();
        template.Archived.Should().BeTrue();
    }

    private CreativeTemplate CreateTestTemplate(long id, string name, CreativeType type, bool predefined)
    {
        CreativeTemplateName templateName = CreativeTemplateName.Create(name, new CreativeTemplateUniqueNameRequirement(true)).Value;

        var creativeFieldUniqueNameRequirement = new CreativeFieldUniqueNameRequirement(true);
        CreativeField creativeField = CreativeField.Create(new CreativeFieldId(1), "Test Field", CreativeFieldType.SingleLineText, creativeFieldUniqueNameRequirement, false, null).Value;
        var creativeFields = new HashSet<CreativeField> { creativeField };
        var displayOrderFields = new List<DisplayOrderCreativeField> { new DisplayOrderCreativeField(new CreativeFieldId(1), 1) };

        var createRequest = new CreativeTemplateCreateRequest(
            new CreativeTemplateId(id),
            templateName,
            type,
            displayOrderFields,
            creativeFields,
            predefined);

        return CreativeTemplateFactory.Create(createRequest).Value;
    }
}
