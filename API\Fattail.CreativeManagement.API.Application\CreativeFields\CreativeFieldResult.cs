﻿using Fattail.CreativeManagement.API.Domain.CreativeFields;

namespace Fattail.CreativeManagement.API.Application.CreativeFields
{
    public record CreativeFieldResult
    (
        long Id,
        string Name,
        CreativeFieldType Type
    );

    public record SelectOptionResult
    (
        long Id,
        string Description
    );

    public record MultiSelectCreativeFieldResult
    (
        long Id,
        string Name,
        CreativeFieldType Type,
        IReadOnlyList<SelectOptionResult> Options
    ) : CreativeFieldResult(Id, Name, Type);

    public record SingleSelectCreativeFieldResult (
        long Id,
        string Name,
        CreativeFieldType Type,
        IReadOnlyList<SelectOptionResult> Options
    ) : CreativeFieldResult(Id, Name, Type);
}
