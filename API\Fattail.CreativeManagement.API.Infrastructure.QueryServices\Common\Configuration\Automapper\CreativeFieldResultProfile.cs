﻿using AutoMapper;
using Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Entities;
using Fattail.CreativeManagement.API.Infrastructure.QueryServices.CreativeFields;

namespace Fattail.CreativeManagement.API.Infrastructure.QueryServices.Common.Configuration.AutoMapper;

internal sealed class CreativeFieldResultProfile : Profile
{
    public CreativeFieldResultProfile ()
    {
        CreateMap<CreativeField, CreativeFieldQueryResult>()
            .Include(typeof(MultiSelectCreativeField), typeof(MultiSelectCreativeFieldQueryResult))
            .Include(typeof(SingleSelectCreativeField), typeof(SingleSelectCreativeFieldQueryResult));

        CreateMap<SelectOption, SelectOptionQueryResult>();
        CreateMap<MultiSelectCreativeField, MultiSelectCreativeFieldQueryResult>();
        CreateMap<SingleSelectCreativeField, SingleSelectCreativeFieldQueryResult>();
    }
}