using AutoMapper;
using Fattail.CreativeManagement.API.Domain.Repositories;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates;
using Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Interfaces;
using Microsoft.Azure.Cosmos;
using Fattail.CreativeManagement.API.Application;
using Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Settings;
using Microsoft.Extensions.Options;

namespace Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Repositories;

internal sealed class CreativeTemplateRepository :
    CosmosDbRepository<CreativeTemplate, CreativeTemplateId, Entities.CreativeTemplate>,
    ICreativeTemplateRepository
{
    public CreativeTemplateRepository (
        ICosmosDbContainerFactory cosmosDbContainerFactory,
        IMapper mapper,
        IOrganizationContext organizationContext,
        IOptions<ParallelExecutionSettings> parallelExecutionSettings,
        ILogger<CreativeTemplateRepository> log) : base(cosmosDbContainerFactory, mapper,
        organizationContext, parallelExecutionSettings, log)
    {
    }

    public override string ContainerName => "CreativeTemplates";

    public override PartitionKey ResolvePartitionKey ()
    {
        return new PartitionKey($"{_organizationContext.OrganizationId}");
    }
}