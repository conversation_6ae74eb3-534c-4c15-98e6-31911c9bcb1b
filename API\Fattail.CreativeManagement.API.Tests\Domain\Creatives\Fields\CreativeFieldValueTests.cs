﻿using Fattail.CreativeManagement.API.Domain.CreativeFields;
using Fattail.CreativeManagement.API.Domain.Creatives.Fields;
using Fattail.CreativeManagement.API.Domain.Creatives.Fields.Sanitizers.StrategyProvider;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates.Fields;
using FluentAssertions;
using Moq;
using NUnit.Framework;

namespace Fattail.CreativeManagement.API.Tests.Domain.Creatives.Fields;

[TestFixture]
public class CreativeFieldValueTests
{
    [SetUp]
    public void SetUp ()
    {
        _sanitizerStrategyProviderMock = new Mock<ISanitizerStrategyProvider>();
    }

    private Mock<ISanitizerStrategyProvider> _sanitizerStrategyProviderMock = null!;

    private static readonly CreativeFieldType[] _supportedCreativeFieldTypes = ((CreativeFieldType[])Enum.GetValues(typeof(CreativeFieldType))).Except([CreativeFieldType.SectionDivider]).ToArray();

    [TestCaseSource(nameof(_supportedCreativeFieldTypes))]
    public async Task Supported_creative_field_can_be_created (CreativeFieldType creativeFieldType)
    {
        var creativeField = CreativeFieldIdentifier.Create(new CreativeFieldId(12345), creativeFieldType);

        CreativeFieldValue creativeFieldValue = (await CreativeFieldValue.Create(creativeField, null, new List<ValidationRule>())).Value;

        creativeFieldValue.Should().NotBeNull();
    }
}