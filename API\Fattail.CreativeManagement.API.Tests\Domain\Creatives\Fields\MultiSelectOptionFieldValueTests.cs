﻿using Fattail.CreativeManagement.API.Domain.CreativeFields;
using Fattail.CreativeManagement.API.Domain.CreativeFiles;
using Fattail.CreativeManagement.API.Domain.Creatives.Fields;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates.Fields;
using FluentAssertions;
using FluentResults;
using NUnit.Framework;
using static FluentAssertions.FluentActions;

namespace Fattail.CreativeManagement.API.Tests.Domain.Creatives.Fields
{
    public class MultiSelectOptionFieldValueTests
    {
        private static readonly CreativeFieldType[] _invalidCreativeFieldTypes =
        ((CreativeFieldType[])Enum.GetValues(typeof(CreativeFieldType)))
        .Where(creativeFieldType => creativeFieldType != CreativeFieldType.MultiSelectOption)
        .ToArray();

        [Test]
        public async Task Multi_select_option_field_value_can_be_created ()
        {
            var creativeField = CreativeFieldIdentifier.Create(new CreativeFieldId(12345),
                CreativeFieldType.MultiSelectOption);

            var optionsIds = new List<long> { 111, 112 };

            Result<MultiSelectOptionFieldValue> multiSelectOptionFieldValue = await MultiSelectOptionFieldValue.Create(creativeField, optionsIds, new List<ValidationRule>());

            multiSelectOptionFieldValue.Value.CreativeFieldIdentifier.Should().Be(creativeField);
            multiSelectOptionFieldValue.Value.Value.Should().BeEquivalentTo(optionsIds);
        }

        [Test]
        public async Task Multi_select_option_field_value_can_be_created_without_value ()
        {
            var creativeField = CreativeFieldIdentifier.Create(new CreativeFieldId(12345),
                CreativeFieldType.MultiSelectOption);

            var multiSelectOptionFieldValueWithoutValue =
                (MultiSelectOptionFieldValue)(await CreativeFieldValue.CreateWithoutValue(creativeField, new List<ValidationRule>())).Value;

            multiSelectOptionFieldValueWithoutValue.CreativeFieldIdentifier.Should().Be(creativeField);
            multiSelectOptionFieldValueWithoutValue.Value.Should().BeEmpty();
        }

        [Test]
        public async Task Multi_select_option_field_value_creation_with_invalid_value_type_fails ()
        {
            var creativeField = CreativeFieldIdentifier.Create(new CreativeFieldId(12345),
                CreativeFieldType.MultiSelectOption);

            await Invoking(async () =>
                    await MultiSelectOptionFieldValue.Create(creativeField, new object(), new List<ValidationRule>()))
                .Should().ThrowExactlyAsync<ArgumentException>();
        }

        [TestCaseSource(nameof(_invalidCreativeFieldTypes))]
        public async Task Multi_select_option_field_value_creation_with_invalid_creative_field_type_fails (
            CreativeFieldType invalidCreativeFieldType)
        {
            var creativeField = CreativeFieldIdentifier.Create(new CreativeFieldId(12345), invalidCreativeFieldType);

            await Invoking(async () =>
                    await MultiSelectOptionFieldValue.Create(creativeField,
                        new CreativeFileId(12345), null))
                .Should().ThrowExactlyAsync<ArgumentException>();
        }

        [Test]
        public async Task Multi_select_option_field_value_generates_new_value ()
        {
            var creativeField = CreativeFieldIdentifier.Create(new CreativeFieldId(12345),
                CreativeFieldType.MultiSelectOption);

            var multiSelectOptionFieldValueWithoutValue =
                (MultiSelectOptionFieldValue)(await CreativeFieldValue.CreateWithoutValue(creativeField, new List<ValidationRule>())).Value;

            var optionsIds = new List<long> { 111, 112 };

            var multiSelectOptionFieldValueWithNewValue =
                (MultiSelectOptionFieldValue)(await multiSelectOptionFieldValueWithoutValue.GenerateNewValue(optionsIds, new List<ValidationRule>())).Value;

            multiSelectOptionFieldValueWithoutValue.Value.Should().BeEmpty();
            multiSelectOptionFieldValueWithoutValue.CreativeFieldIdentifier.Should().Be(creativeField);

            multiSelectOptionFieldValueWithNewValue.CreativeFieldIdentifier.Should().Be(creativeField);
            multiSelectOptionFieldValueWithNewValue.Value.Should().BeEquivalentTo(optionsIds);
        }
    }
}
