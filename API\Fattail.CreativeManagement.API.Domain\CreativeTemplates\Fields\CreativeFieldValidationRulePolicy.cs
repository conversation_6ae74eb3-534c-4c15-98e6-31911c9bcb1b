﻿using Fattail.CreativeManagement.API.Domain.CreativeFields;

namespace Fattail.CreativeManagement.API.Domain.CreativeTemplates.Fields;

public class CreativeFieldValidationRulePolicy
{
    private readonly CreativeFieldType _creativeFieldType;

    public CreativeFieldValidationRulePolicy (CreativeFieldType creativeFieldType)
    {
        _creativeFieldType = creativeFieldType;
    }

    public bool Supports (CreativeFieldValidationRuleType validationRuleType)
    {        
        return (_creativeFieldType, validationRuleType) switch
        {
            (CreativeFieldType.SingleLineText, CreativeFieldValidationRuleType.Required) => true,
            (CreativeFieldType.MultiSelectOption, CreativeFieldValidationRuleType.Required) => true,
            (CreativeFieldType.SingleSelectOption, CreativeFieldValidationRuleType.Required) => true,
            (CreativeFieldType.MultiLineText, CreativeFieldValidationRuleType.Required) => true,
            (CreativeFieldType.MultiFileUpload, CreativeFieldValidationRuleType.FileUploadExtensions) => true,
            (CreativeFieldType.MultiFileUpload, CreativeFieldValidationRuleType.Required) => true,
            (CreativeFieldType.MultiFileUpload, CreativeFieldValidationRuleType.FileSize) => true,
            (CreativeFieldType.FileUpload, CreativeFieldValidationRuleType.FileUploadExtensions) => true,
            (CreativeFieldType.FileUpload, CreativeFieldValidationRuleType.Required) => true,
            (CreativeFieldType.FileUpload, CreativeFieldValidationRuleType.FileSize) => true,
            _ => false
        };
    }
}